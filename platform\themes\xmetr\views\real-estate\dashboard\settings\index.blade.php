@extends('plugins/real-estate::themes.dashboard.layouts.master')

  @section('content')
    {{-- <x-core::card>
        <x-core::card.header>
            <x-core::tab class="card-header-tabs">
                <x-core::tab.item
                    id="profile-tab"
                    :label="trans('plugins/real-estate::dashboard.sidebar_information')"
                    :is-active="true"
                />
                <x-core::tab.item
                    id="change-password-tab"
                    :label="trans('plugins/real-estate::dashboard.sidebar_change_password')"
                />
                {!! apply_filters('account_settings_register_content_tabs', null) !!}
            </x-core::tab>
        </x-core::card.header>

        <x-core::card.body>
            <x-core::tab.content>
                <x-core::tab.pane id="profile-tab" :is-active="true">
                    {!! $profileForm !!}
                </x-core::tab.pane>
                <x-core::tab.pane id="change-password-tab">
                    {!! $changePasswordForm !!}
                </x-core::tab.pane>
                {!! apply_filters('account_settings_register_content_tab_inside', null) !!}
            </x-core::tab.content>
        </x-core::card.body>
    </x-core::card> --}}

    <style>
        .select-location-fields {
                display: flex;
                gap: 20px;
            }
        .form-label {
            font-family: var(--title-font-family);
            font-weight: 700;
            font-size: 15px;
            color: rgba(var(--bs-black-rgb), var(--bs-text-opacity)) !important;
        }
        /* #xmetr-real-estate-forms-fronts-profile-form input[name="email"],
        #xmetr-real-estate-forms-fronts-profile-form label[for="email"] {
            display: none;
        } */
        .select-location-fields div:nth-child(1),
        .select-location-fields div:nth-child(3)
        {
            width: 100%;
        }
        .select-location-fields div:nth-child(2),
        .select-location-fields div:nth-child(4)
        {
            display: none
        }
        .preview-image-wrapper{
            width: 140px !important;
        }
        .image-box.image-box-avatar_image{
            display: flex;
            gap: 10px;
        }
        .image-box.image-box-avatar_image > a[data-bb-toggle="image-picker-choose"]{
            border-radius: 12px;
            display: inline-block;
            font-family: var(--title-font-family);
            font-weight: 600;
            font-size: 15px;
            font-style: normal;
            letter-spacing: 0em;
            padding: 13px 30px;
            position: relative;
            overflow: hidden;
            text-align: center;
            z-index: 0;
            width: fit-content;
            height: 50px;
            background-color: #ffffff;
            border: 1px solid var(--headings-color);
            -webkit-transition: all 0.4s ease;
            -moz-transition: all 0.4s ease;
            -ms-transition: all 0.4s ease;
            -o-transition: all 0.4s ease;
            transition: all 0.4s ease;
        }
        .image-box.image-box-avatar_image > a[data-bb-toggle="image-picker-choose"]:before{
            background-color: #181A20;
            content: "";
            height: 100%;
            left: -100%;
            position: absolute;
            top: 0;
            width: 0;
            z-index: -1;
            -webkit-transform: skew(50deg);
            -moz-transform: skew(50deg);
            -o-transform: skew(50deg);
            transform: skew(50deg);
            -webkit-transition: width 0.6s;
            -moz-transition: width 0.6s;
            -o-transition: width 0.6s;
            transition: width 0.6s;
            transform-origin: top left;
        }
        .image-box.image-box-avatar_image > a[data-bb-toggle="image-picker-choose"]:hover{
            border: 1px solid #181A20;
            color: #ffffff;
        }
        .image-box.image-box-avatar_image > a[data-bb-toggle="image-picker-choose"]:hover:before {
            background-color: #181A20;
            height: 100%;
            width: 200%;
        }
        .preview-image-wrapper .preview-image-inner .image-picker-remove-button {
            border-radius: 10px;
            background: #fbf0ee;
            left: 10px;
            right: unset;
            padding: 15px !important;
        }
        .btn-primary{
            background-color: #5e2dc2;
            border-color: #5e2dc2;
            color: #ffffff;
            border-radius: 10px;
            padding: 15px 24px;
            width: 100%;
        }
        .btn-primary:hover{
            background-color: #5026a5;
            border-color: #5026a5;
            color: #ffffff;
        }
        .account_type .form-check-inline:nth-child(3){
            display: none;
        }
        .preview-image-wrapper .preview-image-inner{
                z-index: 0;
        }
    </style>

  <div class="body_content bg-[#F7F7F7] flex flex-col items-center gap-[30px] pt-[30px] pb-[40px]">
    <h4 class="title text-center">{{ __('Edit profile') }}</h4>

    <div class="max-w-[480px] w-full rounded-[15px] bg-white p-[20px] h-fit flex flex-col gap-[20px]" style="box-shadow:0px 5px 15px rgba(0, 0, 0, 0.25);">
      {!! $profileForm !!}
    </div>

  </div>

@stop

@push('scripts')

<script>
// Target the button using a class or any unique identifier
const button = document.querySelector('.btn.image-picker-remove-button');

// Check if the button exists
if (button) {
    // Remove the existing SVG if any
    const existingSvg = button.querySelector('svg');
    if (existingSvg) {
        existingSvg.remove();
    }
    // SVG code as a string
    const svgCode = `
        <svg width="18" height="21" viewBox="0 0 18 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0 5.05392C0 4.56947 0.345378 4.17674 0.771426 4.17674L3.43565 4.17627C3.965 4.16101 4.432 3.77828 4.61212 3.21208C4.61686 3.19719 4.6223 3.17883 4.64183 3.1122L4.75663 2.72052C4.82688 2.48037 4.88808 2.27114 4.97373 2.08413C5.31206 1.34533 5.93805 0.83229 6.66144 0.70094C6.84454 0.667691 7.03846 0.667831 7.26106 0.667991H10.739C10.9616 0.667831 11.1555 0.667691 11.3386 0.70094C12.062 0.83229 12.688 1.34533 13.0263 2.08413C13.112 2.27114 13.1732 2.48037 13.2434 2.72052L13.3582 3.1122C13.3777 3.17883 13.3832 3.19719 13.3879 3.21208C13.5681 3.77828 14.1277 4.16148 14.657 4.17674H17.2285C17.6545 4.17674 17.9999 4.56947 17.9999 5.05392C17.9999 5.53838 17.6545 5.9311 17.2285 5.9311H0.771426C0.345378 5.9311 0 5.53838 0 5.05392Z" fill="#DC6F5A" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M8.59556 20.668H9.40435C12.187 20.668 13.5784 20.668 14.483 19.7821C15.3877 18.8962 15.4802 17.443 15.6653 14.5365L15.932 10.3486C16.0325 8.77163 16.0827 7.98317 15.6288 7.48352C15.175 6.98386 14.4086 6.98386 12.8759 6.98386H5.12401C3.59125 6.98386 2.82487 6.98386 2.37104 7.48352C1.91721 7.98317 1.96743 8.77163 2.06787 10.3486L2.33458 14.5365C2.51969 17.443 2.61224 18.8962 3.51687 19.7821C4.42151 20.668 5.81286 20.668 8.59556 20.668ZM7.24626 10.8565C7.20506 10.4227 6.8375 10.1061 6.42534 10.1495C6.01318 10.1929 5.71248 10.5798 5.75369 11.0136L6.25369 16.2768C6.29491 16.7106 6.66244 17.0272 7.07456 16.9838C7.48676 16.9404 7.78746 16.5535 7.74626 16.1197L7.24626 10.8565ZM11.5745 10.1495C11.9867 10.1929 12.2874 10.5798 12.2462 11.0136L11.7462 16.2768C11.705 16.7106 11.3374 17.0272 10.9253 16.9838C10.5131 16.9404 10.2124 16.5535 10.2536 16.1197L10.7536 10.8565C10.7948 10.4227 11.1624 10.1061 11.5745 10.1495Z" fill="#DC6F5A" />
        </svg>
    `;

    // Inject the SVG inside the button
    button.insertAdjacentHTML('beforeend', svgCode);
}
</script>
    {!! JsValidator::formRequest(\Xmetr\RealEstate\Http\Requests\SettingRequest::class) !!}
    {!! JsValidator::formRequest(\Xmetr\RealEstate\Http\Requests\UpdatePasswordRequest::class) !!}
@endpush

