<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Xmetr\RealEstate\Models\Property;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Migrate commission metadata to commission column
        $commissionMetadata = DB::table('meta_boxes')
            ->where('reference_type', Property::class)
            ->where('meta_key', 'commission')
            ->whereNotNull('meta_value')
            ->where('meta_value', '!=', '')
            ->where('meta_value', '!=', '[]')
            ->where('meta_value', '!=', '[""]')
            ->where('meta_value', '!=', '[null]')
            ->get();

        foreach ($commissionMetadata as $meta) {
            $value = json_decode($meta->meta_value, true);
            
            // Handle different possible formats of metadata value
            if (is_array($value)) {
                $value = $value[0] ?? null;
            }
            
            if ($value !== null && $value !== '' && is_numeric($value)) {
                DB::table('re_properties')
                    ->where('id', $meta->reference_id)
                    ->update(['commission' => (float) $value]);
            }
        }

        // Migrate deposit metadata to deposit column
        $depositMetadata = DB::table('meta_boxes')
            ->where('reference_type', Property::class)
            ->where('meta_key', 'deposit')
            ->whereNotNull('meta_value')
            ->where('meta_value', '!=', '')
            ->where('meta_value', '!=', '[]')
            ->where('meta_value', '!=', '[""]')
            ->where('meta_value', '!=', '[null]')
            ->get();

        foreach ($depositMetadata as $meta) {
            $value = json_decode($meta->meta_value, true);
            
            // Handle different possible formats of metadata value
            if (is_array($value)) {
                $value = $value[0] ?? null;
            }
            
            if ($value !== null && $value !== '' && is_numeric($value)) {
                DB::table('re_properties')
                    ->where('id', $meta->reference_id)
                    ->update(['deposit' => (float) $value]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore metadata from columns (optional - for rollback)
        $properties = DB::table('re_properties')
            ->whereNotNull('commission')
            ->orWhereNotNull('deposit')
            ->get();

        foreach ($properties as $property) {
            if ($property->commission !== null) {
                DB::table('meta_boxes')->updateOrInsert([
                    'reference_type' => Property::class,
                    'reference_id' => $property->id,
                    'meta_key' => 'commission',
                ], [
                    'meta_value' => json_encode([$property->commission]),
                ]);
            }

            if ($property->deposit !== null) {
                DB::table('meta_boxes')->updateOrInsert([
                    'reference_type' => Property::class,
                    'reference_id' => $property->id,
                    'meta_key' => 'deposit',
                ], [
                    'meta_value' => json_encode([$property->deposit]),
                ]);
            }
        }
    }
};
