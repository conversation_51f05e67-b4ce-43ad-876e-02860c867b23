@php
    $relatedProperties = app(\Xmetr\RealEstate\Repositories\Interfaces\PropertyInterface::class)
        ->getRelatedProperties(
            $property->id,
            theme_option('number_of_related_properties', 3),
            \Xmetr\RealEstate\Facades\RealEstateHelper::getPropertyRelationsQuery()
        );
        $relatedPropertiesCount = $relatedProperties->count();
@endphp

@if ($relatedProperties->isNotEmpty())
    <h3 class="title text-center pt30">{{ __('Similar listings') }}</h3>
    <div class="grid gap-[20px] grid-cols-3 max-[1280px]:grid-cols-2 max-[768px]:grid-cols-1">
        @foreach($relatedProperties as $property)

                @include(Theme::getThemeNamespace('views.real-estate.properties.item-grid'), ['property' => $property])

        @endforeach
    </div>

    {{-- @if($relatedPropertiesCount > theme_option('number_of_related_properties', 3)) --}}
    <div class="w-full flex justify-center">
        <a href="{{ $property->city->url }}" class="px-[37px] py-[15px] bg-[#5E2DC2] rounded-[10px]">
        <p class="text-white text-[15px] font-bold">{{ __('Show more') }}</p>
        </a>
    </div>
    {{-- @endif --}}
@endif
