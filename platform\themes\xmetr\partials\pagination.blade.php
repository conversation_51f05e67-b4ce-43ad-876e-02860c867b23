@if ($paginator->hasPages())
    @php
        $isLastPage = !$paginator->hasMorePages();
        $isFirstPage = $paginator->onFirstPage();
        $totalPages = $paginator->lastPage();
    @endphp



    {{-- master pagination --}}
    <div class="w-full text-center">
        {{-- pagination button --}}
    @if ($totalPages > 1)
        <div class="w-full text-center pt20">
            @if (!$isLastPage)
                <a href="{{ $paginator->nextPageUrl() }}"
                   style=" background-color: #181a20; color: #ffffff; font-weight: 700; font-size: 15px; line-height: 20px; padding: 12px 24px; border: none; border-radius: 999px; cursor: pointer; user-select: none; ">
                    {{ __('Next') }}
                </a>@endif
        </div>
    @endif


    
        <div class="w-full text-center pt20 pb50">
        <ul class="flat-pagination properties-pagination inline-flex justify-center gap-2">
            {{-- Previous Page Link --}}
            @if (! $paginator->onFirstPage())
                <li>
                    <a href="{{ $paginator->previousPageUrl() }}" class="page-numbers" aria-label="{{ trans('pagination.previous') }}">
                        <x-core::icon name="ti ti-chevron-left" />
                    </a>
                </li>
            @endif

            {{-- Pagination Elements --}}
            @php
                $lastPage = $paginator->lastPage();
                $currentPage = $paginator->currentPage();
            @endphp

            @foreach ($elements as $element)
                @if (is_string($element))
                    @continue
                @endif

                @if (is_array($element))
                    @php $dotShown = false; @endphp

                    @foreach ($element as $page => $url)
                        @php
                            $showPage = (
                                $page <= 4 ||
                                $page > $lastPage - 2 ||
                                abs($page - $currentPage) <= 1
                            );
                        @endphp

                        @if ($showPage)
                            <li>
                                <a href="{{ $url }}" @class(['page-numbers', 'current' => $page == $currentPage])>{{ $page }}</a>
                            </li>
                            @php $dotShown = false; @endphp
                        @elseif (!$dotShown)
                            <li><span class="page-numbers">...</span></li>
                            @php $dotShown = true; @endphp
                        @endif
                    @endforeach
                @endif
            @endforeach

            {{-- Next Page Link --}}
            @if ($paginator->hasMorePages())
                <li>
                    <a href="{{ $paginator->nextPageUrl() }}" class="page-numbers" aria-label="{{ trans('pagination.next') }}">
                        <x-core::icon name="ti ti-chevron-right" />
                    </a>
                </li>
            @endif
        </ul>
    </div>
@endif
