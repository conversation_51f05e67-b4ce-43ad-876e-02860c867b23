class Location {
    static getStates($el, countryId, $button = null) {
        $.ajax({
            url: $el.data('url'),
            data: {
                country_id: countryId,
            },
            type: 'GET',
            beforeSend: () => {
                $button && $button.prop('disabled', true)
            },
            success: (res) => {
                if (res.error) {
                    Xmetr.showError(res.message)
                } else {
                    let options = ''
                    $.each(res.data, (index, item) => {
                        options += '<option value="' + (item.id || '') + '">' + item.name + '</option>'
                    })

                    $el.html(options)
                }
            },
            complete: () => {
                $button && $button.prop('disabled', false)
            },
        })
    }

    static getCities($el, stateId, $button = null, countryId = null) {
        $.ajax({
            url: $el.data('url'),
            data: {
                state_id: stateId,
                country_id: countryId,
            },
            type: 'GET',
            beforeSend: () => {
                $button && $button.prop('disabled', true)
            },
            success: (res) => {
                if (res.error) {
                    Xmetr.showError(res.message)
                } else {
                    let options = ''
                    $.each(res.data, (index, item) => {
                        options += '<option value="' + (item.id || '') + '" data-slug="' + item.slug + '" data-url="' + item.url + '" data-projects-url="' + item.projects_url + '">' + item.name + '</option>'
                    })

                    $el.html(options)
                    $el.trigger('change')
                }
            },
            complete: () => {
                $button && $button.prop('disabled', false)
            },
        })
    }

    static getDistricts($el, cityId, $button = null, stateId = null, countryId = null) {
        $.ajax({
            url: $el.data('url'),
            data: {
                city_id: cityId,
                state_id: stateId,
                country_id: countryId,
            },
            type: 'GET',
            beforeSend: () => {
                $button && $button.prop('disabled', true)
            },
            success: (res) => {
                if (res.error) {
                    Xmetr.showError(res.message)
                } else {
                    let options = ''
                    $.each(res.data, (index, item) => {
                        options += '<option value="' + (item.id || '') + '" data-slug="' + item.slug + '" data-url="' + item.url + '">' + item.name + '</option>'
                    })

                    $el.html(options)
                    $el.trigger('change')
                }
            },
            complete: () => {
                $button && $button.prop('disabled', false)
            },
        })
    }

    init() {
        const country = 'select[data-type="country"]'
        const state = 'select[data-type="state"]'
        const city = 'select[data-type="city"]'
        const district = 'select[data-type="district"]'

        $(document).on('change', country, function (e) {
            e.preventDefault()

            const $parent = getParent($(e.currentTarget))

            const $state = $parent.find(state)
            const $city = $parent.find(city)
            const $district = $parent.find(district)

            $state.find('option:not([value=""]):not([value="0"])').remove()
            $city.find('option:not([value=""]):not([value="0"])').remove()
            $district.find('option:not([value=""]):not([value="0"])').remove()

            const $button = $(e.currentTarget).closest('form').find('button[type=submit], input[type=submit]')
            const countryId = $(e.currentTarget).val()

            if (countryId) {
                if ($state.length) {
                    Location.getStates($state, countryId, $button)
                    Location.getCities($city, null, $button, countryId)
                    Location.getDistricts($district, null, $button, null, countryId)
                } else {
                    Location.getCities($city, null, $button, countryId)
                    Location.getDistricts($district, null, $button, null, countryId)
                }
            }

            document.getElementById('filter-city_wrapper').style.display = 'block';
        })

        $(document).on('change', state, function (e) {
            e.preventDefault()

            const $parent = getParent($(e.currentTarget))
            const $city = $parent.find(city)
            const $district = $parent.find(district)

            if ($city.length) {
                $city.find('option:not([value=""]):not([value="0"])').remove()
                $district.find('option:not([value=""]):not([value="0"])').remove()
                const stateId = $(e.currentTarget).val()
                const $button = $(e.currentTarget).closest('form').find('button[type=submit], input[type=submit]')

                if (stateId) {
                    Location.getCities($city, stateId, $button)
                    Location.getDistricts($district, null, $button, stateId)
                } else {
                    const countryId = $parent.find(country).val()

                    Location.getCities($city, null, $button, countryId)
                    Location.getDistricts($district, null, $button, null, countryId)
                }
            }
        })

        $(document).on('change', city, function (e) {
            e.preventDefault()

            const $parent = getParent($(e.currentTarget))
            const $district = $parent.find(district)

            if ($district.length) {
                $district.find('option:not([value=""]):not([value="0"])').remove()
                const cityId = $(e.currentTarget).val()
                const $button = $(e.currentTarget).closest('form').find('button[type=submit], input[type=submit]')

                if (cityId) {
                    const stateId = $parent.find(state).val()
                    const countryId = $parent.find(country).val()
                    Location.getDistricts($district, cityId, $button, stateId, countryId)
                }
            }
        })

        // Removed select2 functionality - using regular HTML select elements

        function getParent($el) {
            let $parent = $(document)
            let formParent = $el.data('form-parent')
            if (formParent && $(formParent).length) {
                $parent = $(formParent)
            }

            return $parent
        }
    }
}

$(() => {
    new Location().init()
})
