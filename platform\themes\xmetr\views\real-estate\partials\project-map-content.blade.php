<template id="project-map-content">
    <div class="map-listing-item">
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden w-80 relative">
            <!-- Close Button -->
            <button class="absolute top-3 right-3 w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center z-10 hover:bg-gray-100 transition-colors">
                <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>

            <!-- Image Section -->
            <div class="relative h-48 overflow-hidden">
                <a href="__url__" target="_blank">
                    <img src="__image__" alt="__name__" class="w-full h-full object-cover hover:scale-105 transition-transform duration-300">
                </a>
            </div>

            <!-- Content Section -->
            <div class="p-4">
                <!-- Price -->
                <div class="text-2xl font-bold text-gray-900 mb-2">__price__</div>

                <!-- Project Details -->
                __project_details__

                <!-- Project Title -->
                <h3 class="font-semibold text-gray-900 mb-1 line-clamp-1">
                    <a href="__url__" title="__name__" class="hover:text-blue-600 transition-colors">__name__</a>
                </h3>

                <!-- Location -->
                <p class="text-gray-600 text-sm mb-4 flex items-center gap-1">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                    </svg>
                    __location__
                </p>

                <!-- Contact Buttons -->
                <div class="flex items-center gap-[10px]">
                    __phone_button__
                    __whatsapp_button__
                    __telegram_button__
                </div>
            </div>
        </div>
    </div>
</template>
