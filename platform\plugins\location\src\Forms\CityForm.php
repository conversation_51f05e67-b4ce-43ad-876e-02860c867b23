<?php

namespace Xmetr\Location\Forms;

use Xmetr\Base\Facades\Assets;
use Xmetr\Base\Forms\FieldOptions\IsDefaultFieldOption;
use Xmetr\Base\Forms\FieldOptions\MediaImageFieldOption;
use Xmetr\Base\Forms\FieldOptions\NameFieldOption;
use Xmetr\Base\Forms\FieldOptions\SortOrderFieldOption;
use Xmetr\Base\Forms\FieldOptions\StatusFieldOption;
use Xmetr\Base\Forms\Fields\MediaImageField;
use Xmetr\Base\Forms\Fields\NumberField;
use Xmetr\Base\Forms\Fields\OnOffField;
use Xmetr\Base\Forms\Fields\SelectField;
use Xmetr\Base\Forms\Fields\TextField;
use Xmetr\Base\Forms\FormAbstract;
use Xmetr\Location\Http\Requests\CityRequest;
use Xmetr\Location\Models\City;
use Xmetr\Location\Models\Country;

class CityForm extends FormAbstract
{
    public function setup(): void
    {
        Assets::addScriptsDirectly('vendor/core/plugins/location/js/location.js');

        $countries = Country::query()->pluck('name', 'id')->all();

        $states = [];
        if ($this->getModel()) {
            $states = $this->getModel()->country->states()->pluck('name', 'id')->all();
        }

        $this
            ->model(City::class)
            ->setValidatorClass(CityRequest::class)
            ->add('name', TextField::class, NameFieldOption::make()->required())
            // ->add('slug', TextField::class, [
            //     'label' => __('Slug'),
            //     'attr' => [
            //         'placeholder' => __('Slug'),
            //         'data-counter' => 120,
            //     ],
            // ])
            ->add('country_id', SelectField::class, [
                'label' => trans('plugins/location::city.country'),
                'required' => true,
                'attr' => [
                    'id' => 'country_id',
                    'class' => 'form-select',
                    'data-type' => 'country',
                ],
                'choices' => [0 => trans('plugins/location::city.select_country')] + $countries,
            ])
            ->add('state_id', SelectField::class, [
                'label' => trans('plugins/location::city.state'),
                'attr' => [
                    'id' => 'state_id',
                    'data-url' => route('ajax.states-by-country'),
                    'class' => 'form-select',
                    'data-type' => 'state',
                ],
                'choices' => ($this->getModel()->state_id ?
                        [
                            0 => trans('plugins/location::city.select_state'),
                            $this->model->state->id => $this->model->state->name,
                        ]
                        :
                        [0 => trans('plugins/location::city.select_state')]) + $states,
            ])
            ->add('order', NumberField::class, SortOrderFieldOption::make())
            ->add('is_default', OnOffField::class, IsDefaultFieldOption::make())
            ->add('status', SelectField::class, StatusFieldOption::make())
            ->add('image', MediaImageField::class, MediaImageFieldOption::make())
            ->setBreakFieldPoint('status');
    }
}
