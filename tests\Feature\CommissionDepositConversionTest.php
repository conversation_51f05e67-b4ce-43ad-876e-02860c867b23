<?php

namespace Tests\Feature;

use Tests\TestCase;
use Xmetr\RealEstate\Models\Property;
use Xmetr\RealEstate\Models\Account;
use Xmetr\RealEstate\Enums\ModerationStatusEnum;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;

class CommissionDepositConversionTest extends TestCase
{
    use RefreshDatabase;

    public function test_commission_and_deposit_are_regular_fields(): void
    {
        // Create a property with commission and deposit
        $property = Property::query()->forceCreate([
            'name' => 'Test Property',
            'commission' => 1500.50,
            'deposit' => 2000.00,
            'price' => 100000,
            'moderation_status' => ModerationStatusEnum::APPROVED,
            'author_type' => Account::class,
        ]);

        // Verify the fields are saved as regular columns
        $this->assertDatabaseHas('re_properties', [
            'id' => $property->id,
            'commission' => 1500.50,
            'deposit' => 2000.00,
        ]);

        // Verify the values can be retrieved directly
        $this->assertEquals(1500.50, $property->commission);
        $this->assertEquals(2000.00, $property->deposit);
    }

    public function test_no_fee_filter_works_with_regular_commission_field(): void
    {
        // Create an owner account
        $owner = Account::query()->forceCreate([
            'first_name' => 'Test',
            'last_name' => 'Owner',
            'email' => '<EMAIL>',
            'account_type' => 'owner',
        ]);

        // Create properties with different commission scenarios
        $propertyWithCommission = Property::query()->forceCreate([
            'name' => 'Property with Commission',
            'author_type' => Account::class,
            'author_id' => $owner->id,
            'commission' => 1500.00,
            'price' => 100000,
            'moderation_status' => ModerationStatusEnum::APPROVED,
        ]);

        $propertyWithZeroCommission = Property::query()->forceCreate([
            'name' => 'Property with Zero Commission',
            'author_type' => Account::class,
            'author_id' => $owner->id,
            'commission' => 0,
            'price' => 100000,
            'moderation_status' => ModerationStatusEnum::APPROVED,
        ]);

        $propertyWithNullCommission = Property::query()->forceCreate([
            'name' => 'Property with Null Commission',
            'author_type' => Account::class,
            'author_id' => $owner->id,
            'commission' => null,
            'price' => 100000,
            'moderation_status' => ModerationStatusEnum::APPROVED,
        ]);

        // Test the no_fee filter query
        $noFeeProperties = Property::query()
            ->where('author_type', Account::class)
            ->whereHas('author', function ($query): void {
                $query->where('account_type', 'owner');
            })
            ->where(function ($query): void {
                $query->whereNull('commission')
                    ->orWhere('commission', 0);
            })
            ->get();

        // Should include properties with null or 0 commission, but not those with actual commission
        $this->assertCount(2, $noFeeProperties);
        $this->assertTrue($noFeeProperties->contains($propertyWithZeroCommission));
        $this->assertTrue($noFeeProperties->contains($propertyWithNullCommission));
        $this->assertFalse($noFeeProperties->contains($propertyWithCommission));
    }

    public function test_commission_and_deposit_are_cast_to_float(): void
    {
        $property = Property::query()->forceCreate([
            'name' => 'Test Property for Casting',
            'commission' => '1500.50',
            'deposit' => '2000.00',
            'price' => 100000,
            'moderation_status' => ModerationStatusEnum::APPROVED,
            'author_type' => Account::class,
        ]);

        $this->assertIsFloat($property->commission);
        $this->assertIsFloat($property->deposit);
        $this->assertEquals(1500.50, $property->commission);
        $this->assertEquals(2000.00, $property->deposit);
    }

    public function test_commission_and_deposit_can_be_null(): void
    {
        $property = Property::query()->forceCreate([
            'name' => 'Test Property with Nulls',
            'commission' => null,
            'deposit' => null,
            'price' => 100000,
            'moderation_status' => ModerationStatusEnum::APPROVED,
            'author_type' => Account::class,
        ]);

        $this->assertNull($property->commission);
        $this->assertNull($property->deposit);
    }
}
