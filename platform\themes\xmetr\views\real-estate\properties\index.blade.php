@php
    $itemLayout ??= request()->input('layout', 'grid');
    $itemLayout = in_array($itemLayout, ['grid', 'list']) ? $itemLayout : 'grid';
    $layout ??= get_property_listing_page_layout();
    $rented_overlay ??= false;

    if (! isset($itemsPerRow)) {
        $itemsPerRow = $itemLayout === 'grid' ? 3 : 2;
        if (! in_array($layout, ['top-map', 'without-map'])) {
            $itemsPerRow = $itemLayout === 'grid' ? 2 : 1;
        }
    }
@endphp

@if ($properties->isNotEmpty())
<input type="hidden" id="found-filter-page-title" value="@isset($page_title) {{ $page_title }} @endisset">
<input type="hidden" id="found-listings" value="{{ $properties instanceof \Illuminate\Pagination\LengthAwarePaginator ? $properties->total() : $properties->count() }}">
    @include(Theme::getThemeNamespace("views.real-estate.properties.$itemLayout"), compact('itemsPerRow', 'rented_overlay'))
@else
@php
     SeoHelper::meta()->addMeta('robots', 'noindex, nofollow');
@endphp
    <input type="hidden" id="found-filter-page-title" value="{{ __('No properties found.') }}">
    <div class="alert alert-warning" role="alert" id="no-properties-found-filter-page-title">
        {{ __('No properties found.') }}
    </div>
@endif

@if ($properties instanceof \Illuminate\Pagination\LengthAwarePaginator && $properties->hasPages())
    <div class="justify-content-center wd-navigation mt15">
       @php
    $query = request()->except(['country_id', 'city_id']);
@endphp
        {{-- {{ $properties->withQueryString()->links(Theme::getThemeNamespace('partials.pagination')) }} --}}
       {{ $properties->appends($query)->links(Theme::getThemeNamespace('partials.pagination')) }}
        {{-- {{ $properties->appends(request()->query())->links(Theme::getThemeNamespace('partials.pagination')) }} --}}
        {{-- {{ $properties->setPath(url()->current())->appends(request()->query())->links(Theme::getThemeNamespace('partials.pagination')) }} --}}

    </div>
@endif
