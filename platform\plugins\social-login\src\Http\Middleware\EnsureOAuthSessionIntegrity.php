<?php

namespace Xmetr\SocialLogin\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class EnsureOAuthSessionIntegrity
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Only apply to OAuth-related routes
        if (!$this->isOAuthRoute($request)) {
            return $next($request);
        }

        // Ensure session is started and properly configured
        if (!$request->hasSession()) {
            Log::warning('OAuth request without session', [
                'url' => $request->fullUrl(),
                'route' => $request->route()?->getName(),
            ]);
            
            return redirect()->route('public.account.login')
                ->with('error', __('Session error. Please try logging in again.'));
        }

        // For OAuth redirects, ensure session data integrity
        if ($request->routeIs('auth.social')) {
            $this->ensureSessionIntegrity($request);
        }

        // For OAuth callbacks, validate session state
        if ($request->routeIs('auth.social.callback')) {
            if (!$this->validateCallbackSession($request)) {
                Log::warning('OAuth callback with invalid session state', [
                    'provider' => $request->route('provider'),
                    'session_id' => $request->session()->getId(),
                    'session_data' => $request->session()->all(),
                ]);
                
                return redirect()->route('public.account.login')
                    ->with('error', __('Authentication session expired. Please try logging in again.'));
            }
        }

        return $next($request);
    }

    /**
     * Check if this is an OAuth-related route
     */
    protected function isOAuthRoute(Request $request): bool
    {
        return $request->routeIs(['auth.social', 'auth.social.callback']);
    }

    /**
     * Ensure session integrity for OAuth redirects
     */
    protected function ensureSessionIntegrity(Request $request): void
    {
        // Force session save to ensure data is persisted
        $request->session()->save();
        
        // Set session cookie parameters for better compatibility
        if (!headers_sent()) {
            $sessionConfig = config('session');
            
            setcookie(
                $sessionConfig['cookie'],
                $request->session()->getId(),
                [
                    'expires' => time() + ($sessionConfig['lifetime'] * 60),
                    'path' => $sessionConfig['path'],
                    'domain' => $sessionConfig['domain'],
                    'secure' => $sessionConfig['secure'] ?? $request->isSecure(),
                    'httponly' => $sessionConfig['http_only'] ?? true,
                    'samesite' => $sessionConfig['same_site'] ?? 'lax',
                ]
            );
        }
    }

    /**
     * Validate session state for OAuth callbacks
     */
    protected function validateCallbackSession(Request $request): bool
    {
        $provider = $request->route('provider');
        
        // Check if required session data exists
        $sessionProvider = $request->session()->get('social_login_provider_current');
        $sessionTime = $request->session()->get('social_login_request_time');
        $sessionGuard = $request->session()->get('social_login_guard_current');

        if (!$sessionProvider || !$sessionTime || !$sessionGuard) {
            return false;
        }

        // Validate provider matches
        if ($sessionProvider !== $provider) {
            return false;
        }

        // Check if session is not too old (30 minutes)
        if (time() - $sessionTime > 1800) {
            return false;
        }

        return true;
    }
}
