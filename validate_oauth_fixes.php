<?php

/**
 * OAuth Fixes Validation Script
 * Run this script to validate that all OAuth redirect fixes are properly implemented
 * 
 * Usage: php validate_oauth_fixes.php
 */

echo "=== OAuth Redirect Fixes Validation ===\n\n";

$errors = [];
$warnings = [];

// Check 1: Social Login Controller Enhancements
echo "1. Checking Social Login Controller...\n";
$socialLoginController = __DIR__ . '/platform/plugins/social-login/src/Http/Controllers/SocialLoginController.php';

if (file_exists($socialLoginController)) {
    $content = file_get_contents($socialLoginController);
    
    // Check for enhanced intended URL validation
    if (strpos($content, 'isValidIntendedUrl') !== false) {
        echo "   ✅ Enhanced URL validation method found\n";
    } else {
        $errors[] = "Enhanced URL validation method missing in SocialLoginController";
    }
    
    // Check for intended URL reconstruction
    if (strpos($content, 'reconstructIntendedUrl') !== false) {
        echo "   ✅ URL reconstruction method found\n";
    } else {
        $errors[] = "URL reconstruction method missing in SocialLoginController";
    }
    
    // Check for enhanced logging
    if (strpos($content, 'log_oauth_errors') !== false) {
        echo "   ✅ Enhanced OAuth logging found\n";
    } else {
        $warnings[] = "Enhanced OAuth logging not found in SocialLoginController";
    }
    
} else {
    $errors[] = "SocialLoginController file not found";
}

// Check 2: Login Controller Logout Enhancements
echo "\n2. Checking Login Controller Logout...\n";
$loginController = __DIR__ . '/platform/plugins/real-estate/src/Http/Controllers/Fronts/LoginController.php';

if (file_exists($loginController)) {
    $content = file_get_contents($loginController);
    
    // Check for logout redirect URL determination
    if (strpos($content, 'determineLogoutRedirectUrl') !== false) {
        echo "   ✅ Logout redirect URL determination found\n";
    } else {
        $errors[] = "Logout redirect URL determination missing in LoginController";
    }
    
    // Check for logout URL validation
    if (strpos($content, 'isValidLogoutRedirectUrl') !== false) {
        echo "   ✅ Logout URL validation found\n";
    } else {
        $errors[] = "Logout URL validation missing in LoginController";
    }
    
} else {
    $errors[] = "LoginController file not found";
}

// Check 3: JavaScript Auth Modal Enhancements
echo "\n3. Checking JavaScript Auth Modal...\n";
$authModalJs = __DIR__ . '/platform/themes/xmetr/assets/js/auth-modal.js';
$publicAuthModalJs = __DIR__ . '/public/themes/xmetr/js/auth-modal.js';

foreach ([$authModalJs, $publicAuthModalJs] as $jsFile) {
    $fileName = basename($jsFile);
    
    if (file_exists($jsFile)) {
        $content = file_get_contents($jsFile);
        
        // Check for enhanced social login link updates
        if (strpos($content, 'updateSocialLoginLinks') !== false) {
            echo "   ✅ Enhanced social login link updates found in $fileName\n";
        } else {
            $errors[] = "Enhanced social login link updates missing in $fileName";
        }
        
        // Check for intended URL session storage
        if (strpos($content, 'storeIntendedUrlInSession') !== false) {
            echo "   ✅ Intended URL session storage found in $fileName\n";
        } else {
            $errors[] = "Intended URL session storage missing in $fileName";
        }
        
        // Check for single property page debug logging
        if (strpos($content, 'single property page') !== false) {
            echo "   ✅ Single property page debug logging found in $fileName\n";
        } else {
            $warnings[] = "Single property page debug logging not found in $fileName";
        }
        
    } else {
        $errors[] = "$fileName file not found";
    }
}

// Check 4: Header Template Logout Forms
echo "\n4. Checking Header Template Logout Forms...\n";
$headerTemplate = __DIR__ . '/platform/themes/xmetr/partials/header.blade.php';

if (file_exists($headerTemplate)) {
    $content = file_get_contents($headerTemplate);
    
    // Check for redirect_url hidden fields in logout forms
    $redirectUrlCount = substr_count($content, 'name="redirect_url"');
    if ($redirectUrlCount >= 2) {
        echo "   ✅ Logout forms with redirect_url fields found ($redirectUrlCount forms)\n";
    } else {
        $errors[] = "Logout forms missing redirect_url hidden fields in header template";
    }
    
} else {
    $errors[] = "Header template file not found";
}

// Check 5: Routes Configuration
echo "\n5. Checking Routes Configuration...\n";
$frontsRoutes = __DIR__ . '/platform/plugins/real-estate/routes/fronts.php';

if (file_exists($frontsRoutes)) {
    $content = file_get_contents($frontsRoutes);
    
    // Check for store-intended-url route
    if (strpos($content, 'store-intended-url') !== false) {
        echo "   ✅ Store intended URL route found\n";
    } else {
        $errors[] = "Store intended URL route missing in fronts.php";
    }
    
} else {
    $errors[] = "Fronts routes file not found";
}

// Check 6: OAuth Session Integrity Middleware
echo "\n6. Checking OAuth Session Integrity Middleware...\n";
$oauthMiddleware = __DIR__ . '/platform/plugins/social-login/src/Http/Middleware/EnsureOAuthSessionIntegrity.php';

if (file_exists($oauthMiddleware)) {
    echo "   ✅ OAuth session integrity middleware found\n";
    
    $content = file_get_contents($oauthMiddleware);
    if (strpos($content, 'validateCallbackSession') !== false) {
        echo "   ✅ Callback session validation found\n";
    } else {
        $warnings[] = "Callback session validation not found in OAuth middleware";
    }
} else {
    $warnings[] = "OAuth session integrity middleware not found";
}

// Check 7: Social Login Routes Middleware
echo "\n7. Checking Social Login Routes...\n";
$socialRoutes = __DIR__ . '/platform/plugins/social-login/routes/web.php';

if (file_exists($socialRoutes)) {
    $content = file_get_contents($socialRoutes);
    
    // Check if OAuth middleware is registered
    if (strpos($content, 'EnsureOAuthSessionIntegrity') !== false) {
        echo "   ✅ OAuth session integrity middleware registered in routes\n";
    } else {
        $warnings[] = "OAuth session integrity middleware not registered in social login routes";
    }
} else {
    $errors[] = "Social login routes file not found";
}

// Check 8: Configuration Files
echo "\n8. Checking Configuration Files...\n";
$socialConfig = __DIR__ . '/platform/plugins/social-login/config/general.php';

if (file_exists($socialConfig)) {
    $content = file_get_contents($socialConfig);
    
    // Check for OAuth configuration
    if (strpos($content, 'oauth') !== false) {
        echo "   ✅ OAuth configuration section found\n";
    } else {
        $warnings[] = "OAuth configuration section not found in social login config";
    }
} else {
    $warnings[] = "Social login configuration file not found";
}

// Summary
echo "\n=== Validation Summary ===\n";

if (empty($errors)) {
    echo "✅ All critical fixes are properly implemented!\n";
} else {
    echo "❌ Critical issues found:\n";
    foreach ($errors as $error) {
        echo "   - $error\n";
    }
}

if (!empty($warnings)) {
    echo "\n⚠️  Warnings (non-critical):\n";
    foreach ($warnings as $warning) {
        echo "   - $warning\n";
    }
}

if (empty($errors)) {
    echo "\n🎉 Your OAuth redirect fixes are ready for testing!\n";
    echo "\nNext steps:\n";
    echo "1. Clear application cache: php artisan cache:clear\n";
    echo "2. Clear browser cache and cookies\n";
    echo "3. Follow the testing guide in OAUTH_REDIRECT_FIXES_TESTING_GUIDE.md\n";
    echo "4. Test all scenarios across different page types\n";
} else {
    echo "\n❌ Please fix the critical issues before testing.\n";
}

echo "\n=== End of Validation ===\n";
