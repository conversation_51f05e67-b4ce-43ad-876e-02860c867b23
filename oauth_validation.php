<?php

/**
 * OAuth Configuration Validation Script
 * Run this script to validate your OAuth setup
 * 
 * Usage: php oauth_validation.php
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load Laravel application
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Google OAuth Configuration Validation ===\n\n";

// Check 1: Environment Configuration
echo "1. Checking Environment Configuration...\n";
$appUrl = config('app.url');
$sessionDriver = config('session.driver');
$sessionLifetime = config('session.lifetime');

echo "   APP_URL: " . ($appUrl ?: 'NOT SET') . "\n";
echo "   SESSION_DRIVER: " . ($sessionDriver ?: 'NOT SET') . "\n";
echo "   SESSION_LIFETIME: " . ($sessionLifetime ?: 'NOT SET') . " minutes\n";

if (!$appUrl) {
    echo "   ❌ APP_URL is not set in .env file\n";
} else {
    echo "   ✅ APP_URL is configured\n";
}

// Check 2: Social Login Settings
echo "\n2. Checking Social Login Settings...\n";
$socialLoginEnabled = setting('social_login_enable');
$googleEnabled = setting('social_login_google_enable');
$googleAppId = setting('social_login_google_app_id');
$googleAppSecret = setting('social_login_google_app_secret');

echo "   Social Login Enabled: " . ($socialLoginEnabled ? 'YES' : 'NO') . "\n";
echo "   Google Login Enabled: " . ($googleEnabled ? 'YES' : 'NO') . "\n";
echo "   Google App ID: " . ($googleAppId ? 'SET' : 'NOT SET') . "\n";
echo "   Google App Secret: " . ($googleAppSecret ? 'SET' : 'NOT SET') . "\n";

if (!$socialLoginEnabled) {
    echo "   ❌ Social login is not enabled\n";
} elseif (!$googleEnabled) {
    echo "   ❌ Google login is not enabled\n";
} elseif (!$googleAppId || !$googleAppSecret) {
    echo "   ❌ Google credentials are not configured\n";
} else {
    echo "   ✅ Google OAuth is properly configured\n";
}

// Check 3: Session Storage
echo "\n3. Checking Session Storage...\n";
if ($sessionDriver === 'file') {
    $sessionPath = storage_path('framework/sessions');
    $isWritable = is_writable($sessionPath);
    $sessionFiles = glob($sessionPath . '/*');
    
    echo "   Session Path: $sessionPath\n";
    echo "   Writable: " . ($isWritable ? 'YES' : 'NO') . "\n";
    echo "   Session Files: " . count($sessionFiles) . "\n";
    
    if (!$isWritable) {
        echo "   ❌ Session directory is not writable\n";
    } else {
        echo "   ✅ Session storage is working\n";
    }
} else {
    echo "   Session Driver: $sessionDriver\n";
    echo "   ✅ Using $sessionDriver for session storage\n";
}

// Check 4: Routes
echo "\n4. Checking OAuth Routes...\n";
try {
    $socialRoute = route('auth.social', 'google');
    $callbackRoute = route('auth.social.callback', 'google');
    
    echo "   Social Login Route: $socialRoute\n";
    echo "   Callback Route: $callbackRoute\n";
    echo "   ✅ OAuth routes are registered\n";
} catch (Exception $e) {
    echo "   ❌ OAuth routes are not properly registered\n";
    echo "   Error: " . $e->getMessage() . "\n";
}

// Check 5: Middleware
echo "\n5. Checking Middleware Configuration...\n";
$webMiddleware = config('app.middleware_groups.web', []);
$hasSession = in_array(\Illuminate\Session\Middleware\StartSession::class, $webMiddleware);
$hasCsrf = in_array(\App\Http\Middleware\VerifyCsrfToken::class, $webMiddleware);

echo "   Session Middleware: " . ($hasSession ? 'REGISTERED' : 'NOT FOUND') . "\n";
echo "   CSRF Middleware: " . ($hasCsrf ? 'REGISTERED' : 'NOT FOUND') . "\n";

if ($hasSession && $hasCsrf) {
    echo "   ✅ Required middleware is configured\n";
} else {
    echo "   ❌ Missing required middleware\n";
}

// Check 6: File Permissions
echo "\n6. Checking File Permissions...\n";
$directories = [
    'storage/framework/sessions',
    'storage/logs',
    'bootstrap/cache',
];

$allWritable = true;
foreach ($directories as $dir) {
    $path = base_path($dir);
    $writable = is_writable($path);
    echo "   $dir: " . ($writable ? 'WRITABLE' : 'NOT WRITABLE') . "\n";
    if (!$writable) {
        $allWritable = false;
    }
}

if ($allWritable) {
    echo "   ✅ All required directories are writable\n";
} else {
    echo "   ❌ Some directories are not writable\n";
}

// Check 7: Database Connection (if using database sessions)
if ($sessionDriver === 'database') {
    echo "\n7. Checking Database Session Table...\n";
    try {
        $sessionTable = config('session.table', 'sessions');
        $exists = Schema::hasTable($sessionTable);
        
        if ($exists) {
            $count = DB::table($sessionTable)->count();
            echo "   Session Table: EXISTS\n";
            echo "   Session Records: $count\n";
            echo "   ✅ Database session table is ready\n";
        } else {
            echo "   ❌ Session table '$sessionTable' does not exist\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Database connection error: " . $e->getMessage() . "\n";
    }
}

// Summary
echo "\n=== Validation Summary ===\n";
$issues = [];

if (!$appUrl) $issues[] = "APP_URL not configured";
if (!$socialLoginEnabled) $issues[] = "Social login not enabled";
if (!$googleEnabled) $issues[] = "Google login not enabled";
if (!$googleAppId || !$googleAppSecret) $issues[] = "Google credentials missing";
if ($sessionDriver === 'file' && !is_writable(storage_path('framework/sessions'))) {
    $issues[] = "Session directory not writable";
}

if (empty($issues)) {
    echo "✅ All checks passed! Your OAuth configuration looks good.\n";
    echo "\nNext steps:\n";
    echo "1. Test OAuth flow on different pages\n";
    echo "2. Monitor logs during testing\n";
    echo "3. Verify session cleanup after logout\n";
} else {
    echo "❌ Issues found:\n";
    foreach ($issues as $issue) {
        echo "   - $issue\n";
    }
    echo "\nPlease fix these issues before testing OAuth functionality.\n";
}

echo "\n=== End of Validation ===\n";
