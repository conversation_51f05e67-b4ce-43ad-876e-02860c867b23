<?php

namespace Xmetr\Location\Forms;

use Xmetr\Base\Facades\Assets;
use Xmetr\Base\Forms\FieldOptions\IsDefaultFieldOption;
use Xmetr\Base\Forms\FieldOptions\MediaImageFieldOption;
use Xmetr\Base\Forms\FieldOptions\NameFieldOption;
use Xmetr\Base\Forms\FieldOptions\SortOrderFieldOption;
use Xmetr\Base\Forms\FieldOptions\StatusFieldOption;
use Xmetr\Base\Forms\Fields\MediaImageField;
use Xmetr\Base\Forms\Fields\NumberField;
use Xmetr\Base\Forms\Fields\OnOffField;
use Xmetr\Base\Forms\Fields\SelectField;
use Xmetr\Base\Forms\Fields\TextField;
use Xmetr\Base\Forms\Fields\TextareaField;
use Xmetr\Base\Forms\Fields\HtmlField;
use Xmetr\Base\Forms\FormAbstract;
use Xmetr\Location\Http\Requests\DistrictRequest;
use Xmetr\Location\Models\City;
use Xmetr\Location\Models\Country;
use Xmetr\Location\Models\District;
use Xmetr\Location\Models\State;

class DistrictForm extends FormAbstract
{
    public function setup(): void
    {
        Assets::addScriptsDirectly('vendor/core/plugins/location/js/location.js');
        $countries = Country::query()
            ->wherePublished()
            ->orderBy('order')
            ->oldest('name')
            ->pluck('name', 'id')
            ->all();

        $states = [];
        $cities = [];

        if ($this->getModel() && $this->getModel()->country_id) {
            $states = State::query()
                ->where('country_id', $this->getModel()->country_id)
                ->wherePublished()
                ->orderBy('order')
                ->oldest('name')
                ->pluck('name', 'id')
                ->all();

            if ($this->getModel()->state_id) {
                $cities = City::query()
                    ->where('state_id', $this->getModel()->state_id)
                    ->wherePublished()
                    ->orderBy('order')
                    ->oldest('name')
                    ->pluck('name', 'id')
                    ->all();
            }
        }

        $this
            ->model(District::class)
            ->setValidatorClass(DistrictRequest::class)
            ->add('name', TextField::class, NameFieldOption::make()->required())
            ->add('country_id', SelectField::class, [
                'label' => trans('plugins/location::district.country'),
                'required' => true,
                'attr' => [
                    'id' => 'country_id',
                    'class' => 'form-select',
                    'data-type' => 'country',
                ],
                'choices' => [0 => trans('plugins/location::district.select_country')] + $countries,
            ])
            ->add('state_id', SelectField::class, [
                'label' => trans('plugins/location::district.state'),
                'attr' => [
                    'id' => 'state_id',
                    'data-url' => route('ajax.states-by-country'),
                    'class' => 'form-select',
                    'data-type' => 'state',
                ],
                'choices' => [0 => trans('plugins/location::district.select_state')] + $states,
            ])
            ->add('city_id', SelectField::class, [
                'label' => trans('plugins/location::district.city'),
                'attr' => [
                    'id' => 'city_id',
                    'data-url' => route('ajax.cities-by-state'),
                    'class' => 'form-select',
                    'data-type' => 'city',
                ],
                'choices' => [0 => trans('plugins/location::district.select_city')] + $cities,
            ])
            ->add('district_description', TextareaField::class, [
                'label' => trans('plugins/location::district.district_description'),
                'attr' => [
                    'rows' => 3,
                    'placeholder' => trans('plugins/location::district.district_description_placeholder'),
                ],
            ])
            ->add('district_ratings_html', HtmlField::class, [
                'html' => $this->getDistrictRatingsHtml(),
            ])
            ->add('order', NumberField::class, SortOrderFieldOption::make())
            ->add('is_default', OnOffField::class, IsDefaultFieldOption::make())
            ->add('status', SelectField::class, StatusFieldOption::make())
            ->add('image', MediaImageField::class, MediaImageFieldOption::make())
            ->setBreakFieldPoint('status');
    }

    protected function getDistrictRatingsHtml(): string
    {
        $model = $this->getModel();

        $ratings = [
            [
                'key' => 'amenities',
                'label' => trans('plugins/location::district.amenities_label'),
                'rating' => $model ? $model->amenities_rating : null,
                'comment' => $model ? $model->amenities_comment : null,
            ],
            [
                'key' => 'transport',
                'label' => trans('plugins/location::district.transport_label'),
                'rating' => $model ? $model->transport_rating : null,
                'comment' => $model ? $model->transport_comment : null,
            ],
            [
                'key' => 'safety',
                'label' => trans('plugins/location::district.safety_label'),
                'rating' => $model ? $model->safety_rating : null,
                'comment' => $model ? $model->safety_comment : null,
            ],
            [
                'key' => 'green_spaces',
                'label' => trans('plugins/location::district.green_spaces_label'),
                'rating' => $model ? $model->green_spaces_rating : null,
                'comment' => $model ? $model->green_spaces_comment : null,
            ],
            [
                'key' => 'noise',
                'label' => trans('plugins/location::district.noise_label'),
                'rating' => $model ? $model->noise_rating : null,
                'comment' => $model ? $model->noise_comment : null,
            ],
            [
                'key' => 'rent',
                'label' => trans('plugins/location::district.rent_label'),
                'rating' => $model ? $model->rent_rating : null,
                'comment' => $model ? $model->rent_comment : null,
            ],
            [
                'key' => 'atmosphere',
                'label' => trans('plugins/location::district.atmosphere_label'),
                'rating' => $model ? $model->atmosphere_rating : null,
                'comment' => $model ? $model->atmosphere_comment : null,
            ],
        ];

        $html = '<div class="district-ratings-container">';
        $html .= '<h4>' . trans('plugins/location::district.district_rating') . '</h4>';
        $html .= '<div class="rating-table">';
        $html .= '<div class="rating-header">';
        $html .= '<div class="rating-category">' . trans('plugins/location::district.category_header') . '</div>';
        $html .= '<div class="rating-score">' . trans('plugins/location::district.score_header') . '</div>';
        $html .= '<div class="rating-comments">' . trans('plugins/location::district.comments_header') . '</div>';
        $html .= '</div>';

        foreach ($ratings as $rating) {
            // Extract icon from label if it starts with emoji
            $label = $rating['label'];
            $icon = '';
            if (preg_match('/^(\p{So}+)\s*(.*)$/u', $label, $matches)) {
                $icon = $matches[1];
                $labelText = $matches[2];
            } else {
                $labelText = $label;
            }

            $html .= '<div class="rating-row">';
            $html .= '<div class="rating-category">';
            if ($icon) {
                $html .= '<span class="rating-icon">' . $icon . '</span>';
            }
            $html .= '<span class="rating-label">' . $labelText . '</span>';
            $html .= '</div>';
            $html .= '<div class="rating-score">';
            $html .= '<input type="number" name="' . $rating['key'] . '_rating" ';
            $html .= 'value="' . ($rating['rating'] ?? '') . '" ';
            $html .= 'step="0.1" min="0" max="5" placeholder="0.0" ';
            $html .= 'class="form-control rating-input" />';
            $html .= '</div>';
            $html .= '<div class="rating-comments">';
            $html .= '<textarea name="' . $rating['key'] . '_comment" ';
            $html .= 'placeholder="Enter your comments..." ';
            $html .= 'class="form-control comment-input" rows="2">';
            $html .= $rating['comment'] ?? '';
            $html .= '</textarea>';
            $html .= '</div>';
            $html .= '</div>';
        }

        $html .= '</div>';
        $html .= '</div>';

        // Add CSS
        $html .= '<style>
            .district-ratings-container {
                margin: 20px 0;
                background: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
            .rating-table {
                display: table;
                width: 100%;
                border-collapse: collapse;
            }
            .rating-header {
                display: table-row;
                background: #e9ecef;
                font-weight: bold;
            }
            .rating-header > div {
                display: table-cell;
                padding: 12px;
                border: 1px solid #dee2e6;
                background: #e9ecef;
            }
            .rating-row {
                display: table-row;
            }
            .rating-row > div {
                display: table-cell;
                padding: 12px;
                border: 1px solid #dee2e6;
                vertical-align: middle;
                background: white;
            }
            .rating-category {
                width: 35%;
                font-weight: 500;
            }
            .rating-score {
                width: 20%;
                text-align: center;
            }
            .rating-comments {
                width: 45%;
            }
            .rating-icon {
                font-size: 18px;
                margin-right: 8px;
            }
            .rating-input {
                text-align: center;
            }
            .comment-input {
                width: 100%;
                resize: vertical;
            }
            .rating-row:nth-child(even) > div {
                background: #f8f9fa;
            }
        </style>';

        return $html;
    }
}
