
<div class="flex gap-[20px] items-stretch max-[768px]:flex-col">
    <!-- Large image on the left -->
    @foreach($property->images as $image)
        @if($loop->first)
            <div class="aspect-[16/10] flex-[3] relative rounded-[10px] overflow-hidden">
                <div class="w-full h-full">
                    <a href="{{ RvMedia::getImageUrl($image) }}" data-fancybox="gallery">
                        {!! RvMedia::image($image, $property->name, 'full', false, ['class' => 'w-full h-full']) !!}
                    </a>
                </div>

                <!-- Badges -->
                <div class="flex gap-[5px] flex-wrap items-start absolute top-[20px] left-[20px] z-[2]">
                     <a href="{{ $property->city->url }}" class="p-[15px] bg-[#FFFFFF]/[.80] rounded-[30px] w-[50px] h-[50px] absolute top-[20px] left-[20px]">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M15.8337 10H4.16699" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M10 15.8332L4.16667 9.99984L10 4.1665" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                     </a>
                    @if($property->is_featured)
                    <div class="rounded-[5px] bg-[#E45E45] px-[10px] py-[5px]">
                      <p class="text-white text-[13px] font-bold">⚡ {{ __('Featured') }}</p>
                    </div>
                    @endif
                </div>

                @if ($property->status != \Xmetr\RealEstate\Enums\PropertyStatusEnum::RENTED)
                @if (RealEstateHelper::isEnabledWishlist())

                <button type="button" class="x-favorite p-[15px] bg-[#5E2DC2]/[.15] rounded-[30px] w-[50px] h-[50px] absolute top-[20px] right-[20px]"
                        data-type="property"
                        data-bb-toggle="add-to-wishlist"
                        data-id="{{ $property->getKey() }}"
                        data-add-message="{{ __('Added ":name" to wishlist successfully!', ['name' => $property->name]) }}"
                        data-remove-message="{{ __('Removed ":name" from wishlist successfully!', ['name' => $property->name]) }}"
                >
                <svg class="x-favorite_icon x-favorite-notFilled" width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M2.43762 3.55751C1.79286 4.66283 1.58065 6.19655 1.97681 7.7812C2.11937 8.35136 2.53381 9.08448 3.19064 9.9283C3.83472 10.7557 4.65681 11.6207 5.52752 12.4505C7.25399 14.0962 9.11202 15.5477 10 16.219C10.888 15.5477 12.746 14.0962 14.4724 12.4505C15.3432 11.6207 16.1653 10.7557 16.8093 9.9283C17.4662 9.08448 17.8806 8.35136 18.0232 7.7812C18.4193 6.19655 18.2072 4.66284 17.5624 3.55751C16.933 2.47865 15.8822 1.77704 14.4426 1.77704C13.1901 1.77704 12.3074 2.70312 11.6832 3.95143C11.3833 4.55124 11.1795 5.16002 11.0505 5.62445C10.9864 5.85497 10.942 6.04579 10.9139 6.17673C10.8145 6.64071 10.2793 6.66389 10 6.66389C9.72074 6.66389 9.18461 6.63639 9.08607 6.17673C9.058 6.0458 9.01357 5.85497 8.94951 5.62445C8.8205 5.16002 8.61667 4.55125 8.3168 3.95144C7.69259 2.70312 6.80983 1.77704 5.5574 1.77704C4.11783 1.77704 3.06695 2.47865 2.43762 3.55751ZM10 3.34981C10.0303 3.2856 10.0616 3.22119 10.0938 3.15672C10.8024 1.73947 12.141 0 14.4426 0C16.5571 0 18.1718 1.07542 19.0974 2.66212C20.0075 4.22234 20.2396 6.2427 19.7471 8.21213C19.5139 9.14525 18.9166 10.1142 18.2116 11.0198C17.4939 11.9419 16.6041 12.8738 15.6985 13.7369C13.8866 15.4639 11.9509 16.9726 11.0493 17.6533C10.4268 18.1235 9.57325 18.1235 8.95066 17.6533C8.04911 16.9726 6.11348 15.4639 4.30148 13.7369C3.39594 12.8738 2.50612 11.9419 1.78837 11.0198C1.08338 10.1142 0.486107 9.14525 0.252836 8.21213C-0.239533 6.2427 -0.00747901 4.22235 0.902653 2.66212C1.82822 1.07542 3.44289 0 5.5574 0C7.85901 0 9.19758 1.73947 9.90618 3.15671C9.93843 3.22119 9.96971 3.2856 10 3.34981Z" fill="white"></path>
                </svg>
                <svg class="x-favorite_icon x-favorite-filled" width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M2 3.5L5.5 0.5L10 4.5L14 0.5L18.5 3L19.5 5L18 9.5L15 13L14 14.5L10 17.5L6.5 14L3.5 12L1 8.5L2 3.5Z" fill="#FF0000"></path>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M2.43762 3.55751C1.79286 4.66283 1.58065 6.19655 1.97681 7.7812C2.11937 8.35136 2.53381 9.08448 3.19064 9.9283C3.83472 10.7557 4.65681 11.6207 5.52752 12.4505C7.25399 14.0962 9.11202 15.5477 10 16.219C10.888 15.5477 12.746 14.0962 14.4724 12.4505C15.3432 11.6207 16.1653 10.7557 16.8093 9.9283C17.4662 9.08448 17.8806 8.35136 18.0232 7.7812C18.4193 6.19655 18.2072 4.66284 17.5624 3.55751C16.933 2.47865 15.8822 1.77704 14.4426 1.77704C13.1901 1.77704 12.3074 2.70312 11.6832 3.95143C11.3833 4.55124 11.1795 5.16002 11.0505 5.62445C10.9864 5.85497 10.942 6.04579 10.9139 6.17673C10.8145 6.64071 10.2793 6.66389 10 6.66389C9.72074 6.66389 9.18461 6.63639 9.08607 6.17673C9.058 6.0458 9.01357 5.85497 8.94951 5.62445C8.8205 5.16002 8.61667 4.55125 8.3168 3.95144C7.69259 2.70312 6.80983 1.77704 5.5574 1.77704C4.11783 1.77704 3.06695 2.47865 2.43762 3.55751ZM10 3.34981C10.0303 3.2856 10.0616 3.22119 10.0938 3.15672C10.8024 1.73947 12.141 0 14.4426 0C16.5571 0 18.1718 1.07542 19.0974 2.66212C20.0075 4.22234 20.2396 6.2427 19.7471 8.21213C19.5139 9.14525 18.9166 10.1142 18.2116 11.0198C17.4939 11.9419 16.6041 12.8738 15.6985 13.7369C13.8866 15.4639 11.9509 16.9726 11.0493 17.6533C10.4268 18.1235 9.57325 18.1235 8.95066 17.6533C8.04911 16.9726 6.11348 15.4639 4.30148 13.7369C3.39594 12.8738 2.50612 11.9419 1.78837 11.0198C1.08338 10.1142 0.486107 9.14525 0.252836 8.21213C-0.239533 6.2427 -0.00747901 4.22235 0.902653 2.66212C1.82822 1.07542 3.44289 0 5.5574 0C7.85901 0 9.19758 1.73947 9.90618 3.15671C9.93843 3.22119 9.96971 3.2856 10 3.34981Z" fill="#FF0000"></path>
                </svg>
                  </button>

                @endif
                @endif

@if ($property->status != \Xmetr\RealEstate\Enums\PropertyStatusEnum::RENTED)
                <!-- Price -->
                <div class="absolute p-[20px] bottom-0 left-0 z-[2]">
                    <div class="px-[10px] py-[5px] rounded-[5px] z-[2] bg-white w-fit" style="box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.25);">
                        <p class="text-black text-[17px]"><b class="font-bold">{{ format_price($property->price, $property->currency) }} / {{ strtolower($property->period->label()) }}</b>
                        @if ($property->currency && $property->currency->symbol != get_application_currency()->symbol)
                    <i class="fa fa-info-circle text-[#717171]"  data-bs-toggle="tooltip" title="{{ __('Converted from ') }} {{$property->currency->symbol}} {{ $property->price  }}"></i>
                    @endif
                        </p>
                    </div>
                </div>
                @endif

                <!-- Gradient overlay -->
                <div class="absolute bottom-0 left-0 w-full h-[95px] rounded-b-[10px] pointer-events-none touch-none" style="background: linear-gradient(180deg, rgba(33, 35, 41, 0), rgba(33, 35, 41, .8));"></div>


                @if ($property->status == \Xmetr\RealEstate\Enums\PropertyStatusEnum::RENTED)
                 <div class="absolute top-0 left-0 w-full h-full z-[1] bg-[#212329]/[.8] flex justify-center items-center">
                    <div class="p-[10px] bg-white rounded-[5px]">
                        <p class="text-black text-[20px] font-bold">🙌 {{ __('Rented') }}</p>
                    </div>
                </div>
                @endif


            </div>
        @endif
    @endforeach

@if ($property->status != \Xmetr\RealEstate\Enums\PropertyStatusEnum::RENTED)
    <div class="flex flex-col gap-[12px] flex-1 max-[768px]:flex-row">
        @foreach($property->images as $image)
            @if(!$loop->first && $loop->iteration <= 3)
                <div class="aspect-[16/10] flex">
                    <a href="{{ RvMedia::getImageUrl($image) }}" class="w-full h-full rounded-[10px] overflow-hidden popup-img" data-fancybox="gallery">
                        {!! RvMedia::image($image, $property->name, 'medium-rectangle', false, ['class' => 'w-full h-full']) !!}
                    </a>
                </div>
            @endif
            @if($loop->iteration == 4)
            <div class="aspect-[16/10] flex relative justify-center items-end">
                <a href="{{ RvMedia::getImageUrl($image) }}" class="w-full h-full rounded-[10px] overflow-hidden popup-img" data-fancybox="gallery">
                    {!! RvMedia::image($image, $property->name, 'medium-rectangle', false, ['class' => 'w-full h-full']) !!}
                </a>

                <div class="px-[12px] py-[10px] rounded-[5px] bg-white absolute bottom-[20px] z-[2]" style="box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.25);">
                    <a href="{{ RvMedia::getImageUrl($property->images[3]) }}" data-fancybox="gallery" class="text-[15px] text-black font-bold popup-img">📸 {{ __(':count photo', ['count' => count($property->images)]) }}</a>
                </div>
            </div>
            @endif
        @endforeach

        <!-- "View All Photos" button -->

        @foreach($property->images as $image)

        @if(!$loop->first && $loop->iteration > 4)
        <a href="{{ RvMedia::getImageUrl($image) }}" data-fancybox="gallery" @style(['display: none' => $loop->iteration > 4])>
            {!! RvMedia::image($image, $property->name, 'medium-rectangle', false, ['class' => 'w-full h-full']) !!}
        </a>
        @endif
        @endforeach
    </div>
    @endif
</div>
