// Burger handler
(function () {
  const burgerTriggers = document.querySelectorAll('.burger-menu_trigger');

  burgerTriggers.forEach(el => {
    el.addEventListener('click', toggleBurger)
  })

  function toggleBurger(e) {
    const menu = document.querySelector('#burger-menu');

    if (menu.classList.contains('burger-menu--active')) {
      // Close

      menu.classList.remove('burger-menu--active')
      burgerTriggers.forEach(el => {
        el.classList.remove('burger-menu_trigger--active')

        el.querySelectorAll('.burger-menu_icon--open').forEach(el => el.classList.add('burger-menu_icon--active'))
        el.querySelectorAll('.burger-menu_icon--close').forEach(el => el.classList.remove('burger-menu_icon--active'))
      })

      document.body.style.overflowY = 'visible';
      document.documentElement.style.overflowY = 'visible';
      document.querySelector('#jcont')?.classList.remove('x-hidden')
    } else {
      // Open

      menu.classList.add('burger-menu--active')
      burgerTriggers.forEach(el => {
        el.classList.add('burger-menu_trigger--active')

        el.querySelectorAll('.burger-menu_icon--open').forEach(el => el.classList.remove('burger-menu_icon--active'))
        el.querySelectorAll('.burger-menu_icon--close').forEach(el => el.classList.add('burger-menu_icon--active'))
      })

      document.body.style.overflowY = 'hidden';
      document.documentElement.style.overflowY = 'hidden';
      document.querySelector('#jcont')?.classList.add('x-hidden')
    }
  }
})();

// Apartment favorite handler
(function () {
  document.querySelectorAll('.x-favorite').forEach(el => el.addEventListener('click', toggleFavorite))

  function toggleFavorite(e) {
    const self = e.target;
    const button = self.closest('.x-favorite');

    button.classList.toggle('x-favorite--active')
  }
})();

// Apartment swiper
// Requires importing <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
(function () {
  const swiper = new Swiper('.apartment-swiper', {
    slidesPerView: 1,
    spaceBetween: 0,
    loop: false,
    navigation: {
      nextEl: '.apartment-swiper-button-next',
      prevEl: '.apartment-swiper-button-prev',
    },
    on: {
      init: function () {
        updatePagination(this);
      },
      slideChange: function () {
        updatePagination(this);
      }
    }
  });

  function updatePagination(swiper) {
    const currentSlideElement = swiper.el.querySelector('.current-slide');
    const totalSlidesElement = swiper.el.querySelector('.total-slides');

    if (currentSlideElement && totalSlidesElement) {
      // For looped swiper, use realIndex + 1
      currentSlideElement.textContent = swiper.realIndex + 1;
      // Don't change the total - it's already set correctly in the template
      // totalSlidesElement.textContent = swiper.slides.length - (swiper.loopedSlides * 2 || 0);
    }
  }
})();

// Toggle button handler
(function () {
  document.querySelectorAll('.x-button-toggle').forEach(el => el.addEventListener('click', toggle))

  function toggle(e) {
    const button = e.target.closest('.x-button-toggle');
    const signleSelect = e.target.closest('.x-button-toggle--singleSelect');

    if (signleSelect) {
      signleSelect.querySelectorAll('.x-button-toggle').forEach(el => el.classList.remove('x-button-toggle--active'))
    }

    button.classList.toggle('x-button-toggle--active')
  }
})();

// Fixed position relative to parent
(function () {
  recalc();

  window.addEventListener('resize', recalc)
  window.addEventListener('scroll', recalc)

  document.querySelectorAll('.x-fixedParent_trigger').forEach(el => el.addEventListener('click', () => setTimeout(() => {
    recalc();
  }, 200)))

  function recalc() {
    document.querySelectorAll('.x-fixedParent_wrapper').forEach(el => {
      const wrapper = el;
      const items = wrapper.querySelectorAll('.x-fixedParent_item');

      items.forEach(el => {
        const observer = new IntersectionObserver(e => {
          const dims = wrapper.getBoundingClientRect();

          // Stop scrolling if reach end
          if (e[0].isIntersecting) {
            el.style.position = 'relative';
            el.style.width = '100%';
            el.style.left = '0';
            el.style.padding = '0';
            wrapper.querySelector('.x-fixedParent_shadow').style.display = 'none';
          } else {
            el.style.position = 'fixed';
            el.style.width = `${wrapper.offsetWidth}px`;
            el.style.left = `${dims.x}px`;
            el.style.padding = '20px';
            wrapper.querySelector('.x-fixedParent_shadow').style.display = 'block';
          }
        })

        observer.observe(wrapper.querySelector('.x-fixedParent_end'));
      })
    })
  }
})();

// Subscribe checkboxes
(function () {
  document.querySelectorAll('.x-sub-checkbox').forEach(el => el.addEventListener('click', handleCheckbox))

  function handleCheckbox(e) {
    const button = e.target.closest('.x-sub-checkbox')
    const group = e.target.closest('.x-sub-checkbox_group')

    group.querySelectorAll('.x-sub-checkbox').forEach(el => el.classList.remove('x-sub-checkbox--active'));
    button.classList.add('x-sub-checkbox--active')
  }
})();

// Handle add new property accordions
(function () {
  document.querySelector('.page-addNewProperty')?.querySelectorAll('.x-sub-checkbox').forEach(el => el.addEventListener('click', handleClick))

  function handleClick(e) {
    const parent = e.target.closest('.x-propertyType');
    const self = e.target.closest('.x-sub-checkbox');
    const wrapper = parent.querySelector('.x-propertyType-wrapper');

    // Reset
    document.querySelectorAll('.x-propertyType-wrapper').forEach(el => el.classList.remove('x-propertyType-wrapper--active'))

    // Open
    wrapper.classList.add('x-propertyType-wrapper--active')
  }
})();

// Handle signup hiding fields
(function () {
  const primaryField = document.querySelector('#x-signup-firstname')?.querySelector('input')
  const hiddenFields = document.querySelectorAll('.x-signup-hiddenField');

  primaryField?.addEventListener('input', e => {
    if (e.target.value === '') {
      // Hide

      hiddenFields.forEach(el => el.style.display = 'none')
    } else {
      // Show
      hiddenFields.forEach(el => el.style.display = 'flex')
    }
  })
})();

// Upload avatar handler
(function () {
  const btn = document.querySelector('#selectAvatarButton')
  const input = document.querySelector('#selectAvatarInput')

  if (btn && input) {
    btn.addEventListener('click', () => {
      input.click();
    })

    input.addEventListener('change', () => {
      const file = input.files[0];

      if (file.size > 1048576) {
        return alert('Файл слишком большой!')
      }
    })
  }
})();

// Select language
(function () {
  document.querySelectorAll('.x-language-speakOn_element').forEach(el => el.addEventListener('click', e => {
    const self = e.target.closest('.x-language-speakOn_element')

    self.classList.toggle('x-language-speakOn_element--active')
  }))
})();

// Apartaments popup
//! BELOWE ALL
if (document.querySelector('.apartaments-popup')) {
  $('.apartaments-popup').magnificPopup({
    delegate: 'a',
    type: 'image'
  });
}

document.addEventListener('DOMContentLoaded', function () {
        const accordions = document.querySelectorAll('.accordion-section');

        accordions.forEach(section => {
            const toggle = section.querySelector('.accordion-toggle');
            const content = section.querySelector('.accordion-content');
            const arrow = section.querySelector('.accordion-arrow');

            toggle.addEventListener('click', () => {
                const isOpen = !content.classList.contains('hidden');

                if (isOpen) {
                    content.classList.add('hidden');
                    arrow.classList.remove('rotate-180');
                } else {
                    content.classList.remove('hidden');
                    arrow.classList.add('rotate-180');
                }
            });
        });
    });
