@if ($model->video)

<div class="rental-conditions mb-5">
    <h4 class="title">{{ __('Video') }}</h4>

    @php
    $firstImage = null;
    @endphp

     @foreach($model->images as $image)
        @if($loop->first)
            @php
                $firstImage = $image;
            @endphp
        @endif
    @endforeach

<!-- Video Thumbnail (Trigger) -->
<a href="#" data-bs-toggle="modal" data-bs-target="#videoModal">
    <div class="relative group overflow-hidden rounded-lg aspect-video shadow-md">
        @if($firstImage)
            <img src="{{ RvMedia::getImageUrl($firstImage) }}" alt="{{ $model->name }}"
                 class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105">
        @else
            <div class="w-full h-full bg-gray-200 flex items-center justify-center text-gray-500 text-sm">
                No Image
            </div>
        @endif

        <!-- Play But<PERSON> Overlay -->
        <div class="absolute inset-0 bg-black/50 flex items-center justify-center z-1 transition-opacity duration-300 group-hover:opacity-100">
            <div class="p-3 bg-white/20 backdrop-blur-sm rounded-full">
                <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="30" cy="30" r="30" fill="rgba(0,0,0,0.7)"/>
                    <path d="M23 18L23 42L42 30L23 18Z" fill="white"/>
                </svg>
            </div>
        </div>
    </div>
</a>

<!-- Bootstrap Modal -->
<div class="modal fade" id="videoModal" tabindex="-1" aria-labelledby="videoModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md">
    <div class="modal-content overflow-hidden rounded-lg">
      <div class="modal-body relative p-0 bg-black">
        <!-- Close Button -->
        <button type="button" class="btn-close bg-[#FFFFFF]/[.80] rounded-[30px] w-[50px] h-[50px] absolute top-0 end-0 m-3 z-10" data-bs-dismiss="modal" aria-label="Close"></button>

        <!-- Video -->
        <video id="player" playsinline controls class="w-full h-auto max-h-[90vh]">
            <source src="{{ RvMedia::url($model->video) }}" type="video/mp4" />
        </video>
      </div>
    </div>
  </div>
</div>

<!-- Plyr Styles & JS -->
<link rel="stylesheet" href="https://cdn.plyr.io/3.7.8/plyr.css" />
<script src="https://cdn.plyr.io/3.7.8/plyr.polyfilled.js"></script>

<style>
    :root {
  --plyr-color-main: var(--primary-color); /* Tailwind's teal-500 */
}
</style>
<script>
    const player = new Plyr('#player');

    // Autoplay when modal is shown
    const videoModal = document.getElementById('videoModal');
    videoModal.addEventListener('shown.bs.modal', function () {
        setTimeout(() => {
            player.play();
        }, 100);
    });

    // Pause when modal is hidden
    videoModal.addEventListener('hidden.bs.modal', function () {
        player.pause();
    });
</script>
</div>

<span class="w-full h-[1px] block bg-[#D6D6D7]"></span>

@endif
