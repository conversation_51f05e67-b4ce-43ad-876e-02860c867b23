<?php

return [
    'supported' => [],

    // OAuth session configuration
    'oauth' => [
        // Maximum time (in seconds) for OAuth flow completion
        'session_timeout' => 1800, // 30 minutes

        // Force session save before OAuth redirect
        'force_session_save' => true,

        // Enhanced session validation
        'validate_session_integrity' => true,

        // Log OAuth errors for debugging
        'log_oauth_errors' => true,

        // Allowed redirect domains (for security)
        'allowed_redirect_domains' => [
            parse_url(config('app.url'), PHP_URL_HOST),
        ],
    ],
];
