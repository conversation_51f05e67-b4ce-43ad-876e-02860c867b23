/**
 * Authentication Modal Handler
 * Handles login and registration form submissions in modal dialogs
 * with redirect-after-login functionality
 */

$(document).ready(function() {

    /**
     * Handle login form submission in modal (Xmetr theme)
     */
    $(document).on('submit', '#modalSignin form, #xmetr-login-form', function(e) {
        e.preventDefault();

        const $form = $(this);
        const $button = $form.find('button[type="submit"]');
        const $modal = $('#modalSignin');

        // Show loading state
        $button.prop('disabled', true).addClass('btn-loading');

        // First check if user is already authenticated to prevent CSRF issues
        checkAuthenticationStatus()
            .then(function(authStatus) {
                if (authStatus.logged_in) {
                    // User is already logged in, redirect directly
                    $modal.modal('hide');

                    if (typeof Theme !== 'undefined' && Theme.showSuccess) {
                        Theme.showSuccess('You are already logged in!');
                    }

                    setTimeout(function() {
                        window.location.href = authStatus.redirect_url;
                    }, 1000);

                    return;
                }

                // User is not logged in, proceed with form submission
                submitLoginForm($form, $button, $modal);
            })
            .catch(function(error) {
                console.warn('Auth status check failed, proceeding with login:', error);
                // If auth check fails, proceed with normal login
                submitLoginForm($form, $button, $modal);
            });
    });

    /**
     * Check authentication status via AJAX
     */
    function checkAuthenticationStatus() {
        return new Promise(function(resolve, reject) {
            $.ajax({
                type: 'GET',
                url: '/auth/status',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                    'Referer': window.location.href // Pass current page URL
                },
                timeout: 3000, // 3 second timeout
                success: function(response) {
                    resolve(response);
                },
                error: function(xhr, status, error) {
                    reject(error);
                }
            });
        });
    }

    /**
     * Submit the login form
     */
    function submitLoginForm($form, $button, $modal) {
        $.ajax({
            type: 'POST',
            url: $form.attr('action'),
            data: new FormData($form[0]),
            contentType: false,
            processData: false,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            },
            success: function(response) {
                if (response.error === false) {
                    // Hide modal
                    $modal.modal('hide');

                    // Show success message
                    if (typeof Theme !== 'undefined' && Theme.showSuccess) {
                        Theme.showSuccess(response.message || 'Login successful');
                    }

                    // Redirect to intended URL or default
                    const redirectUrl = response.data && response.data.redirect_url
                        ? response.data.redirect_url
                        : window.location.href;

                    // Small delay to allow modal to close and message to show
                    setTimeout(function() {
                        window.location.href = redirectUrl;
                    }, 500);
                } else {
                    // Show error message
                    if (typeof Theme !== 'undefined' && Theme.showError) {
                        Theme.showError(response.message || 'Login failed');
                    }
                }
            },
            error: function(xhr) {
                // Handle validation errors and other errors
                if (typeof Theme !== 'undefined' && Theme.handleError) {
                    Theme.handleError(xhr);
                } else {
                    let errorMessage = 'An error occurred during login';

                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                        // Handle validation errors
                        const errors = xhr.responseJSON.errors;
                        const errorMessages = [];

                        for (const field in errors) {
                            if (errors.hasOwnProperty(field)) {
                                errorMessages.push(...errors[field]);
                            }
                        }

                        errorMessage = errorMessages.join('<br>');
                    }

                    // Show error message (fallback)
                    alert(errorMessage);
                }
            },
            complete: function() {
                // Reset button state
                $button.prop('disabled', false).removeClass('btn-loading');

                // Refresh recaptcha if available
                if (typeof refreshRecaptcha !== 'undefined') {
                    refreshRecaptcha();
                }
            }
        });
    }

    /**
     * Handle registration form submission in modal (Xmetr theme)
     */
    $(document).on('submit', '#modalSignup form, #xmetr-register-form', function(e) {
        e.preventDefault();

        const $form = $(this);
        const $button = $form.find('button[type="submit"]');
        const $modal = $('#modalSignup');

        // Show loading state
        $button.prop('disabled', true).addClass('btn-loading');

        $.ajax({
            type: 'POST',
            url: $form.attr('action'),
            data: new FormData($form[0]),
            contentType: false,
            processData: false,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            },
            success: function(response) {
                if (response.error === false) {
                    // Hide modal
                    $modal.modal('hide');

                    // Show success message
                    if (typeof Theme !== 'undefined' && Theme.showSuccess) {
                        Theme.showSuccess(response.message || 'Registration successful');
                    }

                    // Redirect to intended URL or default
                    const redirectUrl = response.data && response.data.redirect_url
                        ? response.data.redirect_url
                        : window.location.href;

                    // Small delay to allow modal to close and message to show
                    setTimeout(function() {
                        window.location.href = redirectUrl;
                    }, 500);
                } else {
                    // Show error message
                    if (typeof Theme !== 'undefined' && Theme.showError) {
                        Theme.showError(response.message || 'Registration failed');
                    }
                }
            },
            error: function(xhr) {
                // Handle validation errors and other errors
                if (typeof Theme !== 'undefined' && Theme.handleError) {
                    Theme.handleError(xhr);
                } else {
                    let errorMessage = 'An error occurred during registration';

                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                        // Handle validation errors
                        const errors = xhr.responseJSON.errors;
                        const errorMessages = [];

                        for (const field in errors) {
                            if (errors.hasOwnProperty(field)) {
                                errorMessages.push(...errors[field]);
                            }
                        }

                        errorMessage = errorMessages.join('<br>');
                    }

                    // Show error message (fallback)
                    alert(errorMessage);
                }
            },
            complete: function() {
                // Reset button state
                $button.prop('disabled', false).removeClass('btn-loading');

                // Refresh recaptcha if available
                if (typeof refreshRecaptcha !== 'undefined') {
                    refreshRecaptcha();
                }
            }
        });
    });

    /**
     * Store current URL when login/register modal is opened
     * This ensures we capture the intended URL even if the user
     * opens the modal manually (not through middleware redirect)
     */
    $('#modalSignin, #modalSignup').on('show.bs.modal', function() {
        // Get the current URL, ensuring it's clean
        const currentUrl = window.location.href;

        // Update the intended URL in the hidden fields
        $('#login-intended-url').val(currentUrl);
        $('#register-intended-url').val(currentUrl);

        // Update social login links with intended URL
        updateSocialLoginLinks(currentUrl);

        // Store in session via AJAX for better reliability across page types
        storeIntendedUrlInSession(currentUrl);
    });

    /**
     * Store intended URL in session via AJAX
     * This ensures the URL is available for social login redirects
     */
    function storeIntendedUrlInSession(intendedUrl) {
        // Only store if we have a valid URL and it's not an auth page
        if (!intendedUrl || intendedUrl.includes('/login') || intendedUrl.includes('/register') || intendedUrl.includes('/auth/')) {
            return;
        }

        $.ajax({
            url: '/account/store-intended-url',
            method: 'POST',
            data: {
                intended_url: intendedUrl,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                console.log('Intended URL stored in session:', intendedUrl);
            },
            error: function(xhr, status, error) {
                console.warn('Failed to store intended URL in session:', error);
            }
        });
    }

    /**
     * Update social login links with the intended URL
     */
    function updateSocialLoginLinks(intendedUrl) {
        // Ensure we have a valid intended URL
        if (!intendedUrl || intendedUrl === '') {
            intendedUrl = window.location.href;
        }

        // Clean the intended URL (remove any existing auth parameters)
        try {
            const cleanUrl = new URL(intendedUrl);
            cleanUrl.searchParams.delete('intended_url');
            cleanUrl.searchParams.delete('guard');
            intendedUrl = cleanUrl.toString();
        } catch (e) {
            // If URL parsing fails, use current location
            intendedUrl = window.location.href;
        }

        $('.social-login, .social-icons a').each(function() {
            const $link = $(this);
            const href = $link.attr('href');

            if (href && href.includes('/auth/')) {
                try {
                    // Parse existing URL
                    const url = new URL(href, window.location.origin);

                    // Add or update intended_url parameter
                    url.searchParams.set('intended_url', intendedUrl);
                    url.searchParams.set('guard', 'account');

                    // Update the link
                    $link.attr('href', url.toString());

                    // Debug logging for single property pages
                    if (window.location.pathname.includes('/properties/') && !window.location.pathname.endsWith('/properties')) {
                        console.log('Updated social login link for single property page:', {
                            originalHref: href,
                            newHref: url.toString(),
                            intendedUrl: intendedUrl
                        });
                    }
                } catch (e) {
                    console.error('Error updating social login link:', e);
                }
            }
        });
    }
});
