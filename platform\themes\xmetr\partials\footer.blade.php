{{-- @php
    $topFooterSidebar = dynamic_sidebar('top_footer_sidebar');
    $innerFooterSidebar = dynamic_sidebar('inner_footer_sidebar');
    $bottomFooterSidebar = dynamic_sidebar('bottom_footer_sidebar');
    $footerBackgroundColor = theme_option('footer_background_color', '#161e2d');
    $footerBackgroundImage = RvMedia::getImageUrl(theme_option('footer_background_image'));
@endphp

@if($topFooterSidebar || $innerFooterSidebar || $bottomFooterSidebar)
    <footer class="footer" @style(["background-color: $footerBackgroundColor" => $footerBackgroundColor, "background-image: url('$footerBackgroundImage') !important" => theme_option('footer_background_image')])>
        @if($topFooterSidebar)
            <div class="top-footer">
                <div class="container">
                    <div class="content-footer-top">
                        {!! $topFooterSidebar !!}
                    </div>
                </div>
            </div>
        @endif

        @if($innerFooterSidebar)
            <div class="inner-footer">
                <div class="container">
                    <div class="row">
                        {!! $innerFooterSidebar !!}
                    </div>
                </div>
            </div>
        @endif

        @if($bottomFooterSidebar)
            <div class="bottom-footer">
                <div class="container">
                    <div class="content-footer-bottom">
                        {!! $bottomFooterSidebar !!}
                    </div>
                </div>
            </div>
        @endif
    </footer>
@endif --}}

<!-- Our Footer -->
<section class="footer-style1 pt60 pb-0">
  <div class="container">
    <div class="row">
      <div class="col-xl-12">
        <div class="mailchimp-widget text-center mb-0">
          <h2 class="title text-white">{{ theme_option('footer_heading') }}</h2>
          <h6 class="title text-white fw400 mb20"><EMAIL></h6>
        </div>

        <!-- Telegram block with QR inside -->
        <div class="d-flex justify-content-center align-items-start" style="margin-top: 20px; margin-bottom: 20px;">
          <a href="https://t.me/xmetrsupport" target="_blank" rel="noopener noreferrer" style="text-decoration: none;">
            <div class="d-flex align-items-start transition telegram-block">
              
              <!-- QR inside block (hidden on mobile) -->
              <img class="hide-on-mobile" src="https://media.xmetr.com/general/xmetrsupportqr.webp" alt="Support QR" style="height: 56px; border-radius: 8px; margin-right: 16px;" />

              <!-- Telegram content -->
              <div class="d-flex align-items-center">
                <div class="flex-shrink-0">
                  <i class="fab fa-telegram fz40 text-white"></i>
                </div>
                <div class="ms-3">
                  <h6 class="text-white fz14 mb0">Telegram</h6>
                  <p class="fz13 mb0 text-white-50">🟢 Online</p>
                </div>
              </div>

            </div>
          </a>
        </div>
      </div>
    </div>
  </div>

  <div class="container white-bdrt1 py-4 text-center">
    <div class="row">
      <div class="col-sm-12">
        <div class="text-center">
          <p class="copyright-text text-gray ff-heading">
            {!! theme_option('copyright') !!}

            @php
              $locale = app()->getLocale();

              $privacyUrls = [
                  'en' => 'https://xmetr.com/en/privacy-policy',
                  'ru' => 'https://xmetr.com/ru/privacy-policy',
                  'es' => 'https://xmetr.com/es/privacy-policy',
                  'fr' => 'https://xmetr.com/fr/privacy-policy',
                  'de' => 'https://xmetr.com/de/privacy-policy',
              ];

              $termsUrls = [
                  'en' => 'https://xmetr.com/en/terms-of-use',
                  'ru' => 'https://xmetr.com/ru/terms-of-use',
                  'es' => 'https://xmetr.com/es/terms-of-use',
                  'fr' => 'https://xmetr.com/fr/terms-of-use',
                  'de' => 'https://xmetr.com/de/terms-of-use',
              ];

              $privacyLabels = [
                  'en' => 'Privacy Policy',
                  'ru' => 'Политика конфиденциальности',
                  'es' => 'Política de Privacidad',
                  'fr' => 'Politique de Confidentialité',
                  'de' => 'Datenschutzrichtlinie',
              ];

              $termsLabels = [
                  'en' => 'Terms of Use',
                  'ru' => 'Условия использования',
                  'es' => 'Términos de uso',
                  'fr' => 'Conditions d’utilisation',
                  'de' => 'Nutzungsbedingungen',
              ];
            @endphp

            @if (isset($privacyUrls[$locale], $privacyLabels[$locale]) && isset($termsUrls[$locale], $termsLabels[$locale]))
              &nbsp;|&nbsp;
              <a href="{{ $privacyUrls[$locale] }}" target="_blank" rel="noopener noreferrer" style="text-decoration: underline;">
                {{ $privacyLabels[$locale] }}
              </a>
              &nbsp;|&nbsp;
              <a href="{{ $termsUrls[$locale] }}" target="_blank" rel="noopener noreferrer" style="text-decoration: underline;">
                {{ $termsLabels[$locale] }}
              </a>
            @endif
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  .transition:hover {
    transform: scale(1.03);
    background-color: #2a2b32;
  }

  .telegram-block {
    background-color: #1e1f25;
    border-radius: 16px;
    padding: 12px 16px;
    width: 360px;
    min-height: 80px;
  }

  @media (max-width: 767.98px) {
    .hide-on-mobile {
      display: none !important;
    }
    .telegram-block {
      width: 300px !important;
    }
  }
</style>





  <a class="scrollToHome" href="#"><i class="fas fa-angle-up"></i></a>
</div>