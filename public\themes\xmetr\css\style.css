/* ---------------------------------------------------------
    * Name: Homzen
    * Version: 1.0.1
    * Author: Themesflat
    * Author URI: http://themesflat.com

	* Abstracts variable

    * Reset css styles

    * Components
        * header
        * footer
        * tabs
        * slider banner
        * button
        * range slider
        * form
        * nice select
        * carousel
        * avatar
        * off canvas
        * hover
        * preloader

    * section

    * dashboard

    * Responsive
 ------------------------------------------------------------------------------ */
@font-face {
  font-family: "icomoon";
  src: url("../fonts/icomoon.eot?r95zqu");
  src: url("../fonts/icomoon.eot?r95zqu#iefix") format("embedded-opentype"), url("../fonts/icomoon.ttf?r95zqu") format("truetype"), url("../fonts/icomoon.woff?r95zqu") format("woff"), url("../fonts/icomoon.svg?r95zqu#icomoon") format("svg");
  font-weight: normal;
  font-style: normal;
  font-display: block;
}
[class^=icon-],
[class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: "icomoon" !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-check-circle .path1:before {
  content: "\e900";
  color: rgb(25, 135, 84);
}

.icon-check-circle .path2:before {
  content: "\e901";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}

.icon-img-2:before {
  content: "\e905";
}

.icon-trash:before {
  content: "\e906";
}

.icon-sold:before {
  content: "\e907";
}

.icon-edit:before {
  content: "\e908";
}

.icon-calendar:before {
  content: "\e909";
}

.icon-review:before {
  content: "\e90a";
}

.icon-bookmark:before {
  content: "\e90b";
}

.icon-clock-countdown:before {
  content: "\e90c";
}

.icon-sign-out:before {
  content: "\e90d";
}

.icon-profile:before {
  content: "\e90e";
}

.icon-messages:before {
  content: "\e90f";
}

.icon-file-text:before {
  content: "\e910";
}

.icon-package:before {
  content: "\e911";
}

.icon-list-dashes:before {
  content: "\e912";
}

.icon-dashboard:before {
  content: "\e913";
}

.icon-tick:before {
  content: "\e93b";
}

.icon-minus:before {
  content: "\e916";
}

.icon-plus:before {
  content: "\e917";
}

.icon-play2:before {
  content: "\e918";
}

.icon-360:before {
  content: "\e919";
}

.icon-download:before {
  content: "\e91a";
}

.icon-support:before {
  content: "\e91b";
}

.icon-booking:before {
  content: "\e91c";
}

.icon-guarantee:before {
  content: "\e91e";
}

.icon-secure:before {
  content: "\e91f";
}

.icon-coffee:before {
  content: "\e920";
}

.icon-dishwasher:before {
  content: "\e921";
}

.icon-microwave:before {
  content: "\e922";
}

.icon-refrigerator:before {
  content: "\e923";
}

.icon-tv:before {
  content: "\e924";
}

.icon-iron:before {
  content: "\e925";
}

.icon-pillows:before {
  content: "\e926";
}

.icon-bed-line:before {
  content: "\e927";
}

.icon-hanger:before {
  content: "\e928";
}

.icon-security:before {
  content: "\e929";
}

.icon-lockbox:before {
  content: "\e92a";
}

.icon-kit:before {
  content: "\e92b";
}

.icon-carbon:before {
  content: "\e92c";
}

.icon-smoke-alarm:before {
  content: "\e92d";
}

.icon-home-location:before {
  content: "\e92f";
}

.icon-home:before {
  content: "\e930";
}

.icon-play:before {
  content: "\e931";
}

.icon-hammer:before {
  content: "\e932";
}

.icon-crop:before {
  content: "\e933";
}

.icon-garage:before {
  content: "\e934";
}

.icon-sliders-horizontal:before {
  content: "\e935";
}

.icon-house-line:before {
  content: "\e936";
}

.icon-share:before {
  content: "\e937";
}

.icon-images:before {
  content: "\e938";
}

.icon-map-trifold:before {
  content: "\e939";
}

.icon-list:before {
  content: "\e93a";
}

.icon-grid:before {
  content: "\e93c";
}

.icon-categories:before {
  content: "\e93d";
}

.icon-search:before {
  content: "\e93e";
}

.icon-close2:before {
  content: "\e92e";
}

.icon-villa5:before {
  content: "\e93f";
}

.icon-farm:before {
  content: "\e940";
}

.icon-ware:before {
  content: "\e941";
}

.icon-commercial2:before {
  content: "\e942";
}

.icon-office:before {
  content: "\e943";
}

.icon-commercial:before {
  content: "\e944";
}

.icon-villa:before {
  content: "\e945";
}

.icon-studio:before {
  content: "\e946";
}

.icon-townhouse:before {
  content: "\e947";
}

.icon-apartment:before {
  content: "\e948";
}

.icon-rent-home:before {
  content: "\e949";
}

.icon-sale-home:before {
  content: "\e94a";
}

.icon-buy-home:before {
  content: "\e94b";
}

.icon-double-ruler:before {
  content: "\e94c";
}

.icon-hand:before {
  content: "\e94d";
}

.icon-proven:before {
  content: "\e94e";
}

.icon-send:before {
  content: "\e94f";
}

.icon-mail:before {
  content: "\e950";
}

.icon-phone2:before {
  content: "\e951";
}

.icon-mapPinLine:before {
  content: "\e952";
}

.icon-youtube:before {
  content: "\e953";
}

.icon-pinterest:before {
  content: "\e954";
}

.icon-phone:before {
  content: "\e955";
}

.icon-instagram:before {
  content: "\e956";
}

.icon-twitter:before {
  content: "\e957";
}

.icon-linkedin:before {
  content: "\e958";
}

.icon-facebook:before {
  content: "\e959";
}

.icon-star:before {
  content: "\e95a";
}

.icon-bathtub:before {
  content: "\e95b";
}

.icon-bed:before {
  content: "\e95c";
}

.icon-ruler:before {
  content: "\e95d";
}

.icon-arr-r:before {
  content: "\e91d";
}

.icon-arr-l:before {
  content: "\e95e";
}

.icon-mapPin:before {
  content: "\e95f";
}

.icon-eye:before {
  content: "\e960";
}

.icon-heart:before {
  content: "\e961";
}

.icon-arrLeftRight:before {
  content: "\e963";
}

.icon-location:before {
  content: "\e902";
}

.icon-faders:before {
  content: "\e903";
}

.icon-arr-down:before {
  content: "\e904";
}

.icon-eye-off:before {
  content: "\e915";
}

.icon-arrow-down-right:before {
  content: "\e962";
}

.icon-arrow-right2:before {
  content: "\ea3c";
}

.icon-arrow-left2:before {
  content: "\ea40";
}

.icon-arrow-down-right2:before {
  content: "\ea3d";
}

/*--------- Abstracts variable ---------- */
/*---------- Reset css styles ----------- */
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
  font-family: inherit;
  font-size: 100%;
  font-style: inherit;
  font-weight: inherit;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

/* Elements
-------------------------------------------------------------- */
html {
  margin-right: 0 !important;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--primary-font);
  font-size: 16px;
  line-height: 26px;
  font-weight: 400;
  color: #161e2d;
  background-color: #ffffff;
}

img {
  max-width: 100%;
  height: auto;
  transform: scale(1);
  vertical-align: middle;
  -ms-interpolation-mode: bicubic;
}

.row {
  margin-right: -15px;
  margin-left: -15px;
}
.row > * {
  padding-left: 15px;
  padding-right: 15px;
}

ul,
li {
  list-style-type: none;
  margin-bottom: 0;
  padding-left: 0;
  list-style: none;
}

.center {
  text-align: center;
}

.container4 {
  max-width: 1710px;
}

.container3 {
  max-width: 660px;
}

.container2 {
  max-width: 1100px;
}

.container {
  max-width: 1320px;
}

.container4,
.container3,
.container2,
.container {
  width: 100%;
  margin: auto;
}

.container4,
.container3,
.container2,
.container-fluid,
.container {
  padding-left: 15px;
  padding-right: 15px;
}

.container-full {
  max-width: 100%;
}

.cus-layout-1 {
  width: calc(100vw - (100vw - 1290px) / 2);
  margin-inline-end: unset !important;
  max-width: 100%;
  margin-inline-start: auto;
}

textarea,
input[type=text],
input[type=password],
input[type=datetime],
input[type=datetime-local],
input[type=date],
input[type=month],
input[type=time],
input[type=week],
input[type=number],
input[type=email],
input[type=url],
input[type=search],
input[type=tel],
input[type=color] {
  font-family: var(--heading-font);
  border: 1px solid #e4e4e4;
  outline: 0;
  box-shadow: none;
  font-size: 16px;
  line-height: 26px;
  border-radius: 8px;
  padding: 14px 16px;
  width: 100%;
  background: #ffffff;
  color: #161e2d;
  font-weight: 400;
}
textarea:focus,
input[type=text]:focus,
input[type=password]:focus,
input[type=datetime]:focus,
input[type=datetime-local]:focus,
input[type=date]:focus,
input[type=month]:focus,
input[type=time]:focus,
input[type=week]:focus,
input[type=number]:focus,
input[type=email]:focus,
input[type=url]:focus,
input[type=search]:focus,
input[type=tel]:focus,
input[type=color]:focus {
  border-color: var(--primary-color);
}

textarea::-moz-placeholder, input[type=text]::-moz-placeholder, input[type=password]::-moz-placeholder, input[type=datetime]::-moz-placeholder, input[type=datetime-local]::-moz-placeholder, input[type=date]::-moz-placeholder, input[type=month]::-moz-placeholder, input[type=time]::-moz-placeholder, input[type=week]::-moz-placeholder, input[type=number]::-moz-placeholder, input[type=email]::-moz-placeholder, input[type=url]::-moz-placeholder, input[type=search]::-moz-placeholder, input[type=tel]::-moz-placeholder, input[type=color]::-moz-placeholder {
  color: #a3abb0;
  -moz-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

textarea::placeholder,
input[type=text]::placeholder,
input[type=password]::placeholder,
input[type=datetime]::placeholder,
input[type=datetime-local]::placeholder,
input[type=date]::placeholder,
input[type=month]::placeholder,
input[type=time]::placeholder,
input[type=week]::placeholder,
input[type=number]::placeholder,
input[type=email]::placeholder,
input[type=url]::placeholder,
input[type=search]::placeholder,
input[type=tel]::placeholder,
input[type=color]::placeholder {
  color: #a3abb0;
  transition: all 0.3s ease;
}

textarea {
  height: 112px;
  resize: none;
}

/* Placeholder color */
::-webkit-input-placeholder {
  color: #8a8aa0;
}

:-moz-placeholder {
  color: #8a8aa0;
}

::-moz-placeholder {
  color: #8a8aa0;
  opacity: 1;
}

.error {
  font-size: 16px;
  margin-bottom: 10px;
  transition: all ease 0.3s;
}

/* Since FF19 lowers the opacity of the placeholder by default */
:-ms-input-placeholder {
  color: #8a8aa0;
}

p {
  font-weight: 400;
  font-size: 15px;
  line-height: 22px;
}

.p-12 {
  font-size: 12px;
  line-height: 18px;
}

.p-16 {
  font-size: 16px;
  line-height: 26px;
}

/* Typography
-------------------------------------------------------------- */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--heading-font);
  font-weight: 600;
  text-rendering: optimizeLegibility;
  color: #161e2d;
}

h1 {
  font-size: 80px;
  line-height: 88px;
  font-weight: 700;
}

h2 {
  font-size: 56px;
  line-height: 68px;
  font-weight: 600;
}

h3 {
  font-size: 44px;
  line-height: 62px;
  font-weight: 600;
}

h4 {
  font-size: 36px;
  line-height: 44px;
  font-weight: 600;
}

h5 {
  font-size: 30px;
  line-height: 42px;
}

h6 {
  font-size: 24px;
  line-height: 30px;
  font-weight: 600;
}

a {
  transition: all 0.3s ease;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-block;
  color: #161e2d;
}
a:focus, a:hover {
  transition: all 0.3s ease;
  text-decoration: none;
  outline: 0;
}

label {
  font-family: var(--heading-font);
  font-weight: 600;
}

.link {
  transition: all 0.3s ease;
}
.link:hover {
  color: var(--primary-color) !important;
}

.h7 {
  font-family: var(--heading-font);
  font-size: 20px;
  line-height: 28px;
  font-weight: 600;
}

.body-1 {
  font-size: 20px;
  font-weight: 400;
  line-height: 30px;
}

.body-2 {
  font-size: 18px;
  font-weight: 400;
  line-height: 28px;
}

.text-1 {
  font-size: 18px;
  font-weight: 600;
  line-height: 28px;
}

.text-2 {
  font-size: 16px;
  font-weight: 700;
  line-height: 26px;
}

.text-3 {
  font-size: 14px;
  font-weight: 700;
  line-height: 24px;
  letter-spacing: 0.8px;
}

.caption-1 {
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}

.caption-2 {
  font-size: 12px;
  font-weight: 400;
  line-height: 26px;
}

.text-subheading {
  font-family: var(--heading-font);
  font-size: 12px;
  line-height: 19px;
  font-weight: 600;
  letter-spacing: 0.8px;
  text-transform: uppercase;
}

.text-subtitle {
  font-family: var(--heading-font);
  font-size: 14px;
  line-height: 24px;
  font-weight: 600;
  letter-spacing: 0.8px;
  text-transform: uppercase;
}

.fw-1 {
  font-weight: 100;
}

.fw-4 {
  font-weight: 400;
}

.fw-5 {
  font-weight: 500;
}

.fw-6 {
  font-weight: 600;
}

.fw-7 {
  font-weight: 700;
}

.fw-8 {
  font-weight: 800;
}

.fs-12 {
  font-size: 12px;
}

.fs-13 {
  font-size: 13px;
}

.fs-16 {
  font-size: 16px;
}

.fs-18 {
  font-size: 18px;
}

.fs-20 {
  font-size: 20px;
}

.fs-22 {
  font-size: 22px;
}

.fs-26 {
  font-size: 26px;
}

.fs-30 {
  font-size: 30px;
}

.fs-40 {
  font-size: 40px;
}

.text-primary {
  color: var(--primary-color) !important;
}

.text-danger {
  color: #c72929 !important;
}

.text-black {
  color: #161e2d !important;
}

.text-white {
  color: #ffffff !important;
}

.text-success {
  color: #198754 !important;
}

.text-variant-1 {
  color: #5c6368;
}

.text-variant-2 {
  color: #a3abb0;
}

.bg-surface {
  background-color: #f7f7f7;
}

.my-40 {
  margin-top: 40px;
  margin-bottom: 40px;
}

.mt-4 {
  margin-top: 4px !important;
}

.mt-8 {
  margin-top: 8px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-12 {
  margin-top: 12px;
}

.mt-16 {
  margin-top: 16px;
}

.round-8 {
  border-radius: 8px;
}

.round-12 {
  border-radius: 12px;
}

.grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
}

.grid-6 {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
}

.gap-4 {
  gap: 4px !important;
}

.gap-6 {
  gap: 6px !important;
}

.gap-8 {
  gap: 8px;
}

.gap-12 {
  gap: 12px;
}

.gap-16 {
  gap: 16px;
}

.gap-20 {
  gap: 20px;
}

.gap-30 {
  gap: 30px;
}

.pt-0 {
  padding-top: 0px !important;
}

.no-line {
  border: 0 !important;
}

/*------------ Components ---------------- */
/*------------ header ---------------- */
/* Header
-------------------------------------------------------------- */
.header-account {
  display: flex;
  align-items: center;
}
.header-account ul {
  margin-right: 20px;
}
.header-account ul li {
  font-weight: 700;
}
.header-account ul li a {
  font-family: var(--heading-font);
  color: #161e2d;
  font-weight: 600;
}
.header-account ul li a:hover {
  color: var(--primary-color);
}

.header-style-2 .header-account ul li {
  color: #ffffff;
}
.header-style-2 .header-account ul li a {
  color: #ffffff;
}
.header-style-2 .header-account ul li a:hover {
  color: var(--primary-color);
}

.main-header {
  z-index: 999;
  width: 100%;
  height: 80px;
  position: relative;
  border-bottom: 1px solid #e4e4e4;
  background-color: #ffffff;
  padding-left: 30px;
  padding-right: 30px;
}
.main-header.header-style-2 {
  margin-bottom: -80px;
  border: 0;
  background-color: transparent;
  transition: all 0.5s ease;
}
.main-header.header-style-2.is-fixed {
  background-color: #ffffff;
  border-bottom: 1px solid #e4e4e4;
}
.main-header.header-style-2.is-fixed .navigation > li > a {
  color: #161e2d !important;
}
.main-header.header-style-2.is-fixed .header-account ul li a {
  color: #161e2d !important;
}

.wrap-top {
  width: 100%;
  height: 78px;
  position: relative;
  padding: 12px 0px 0 0px;
}
.wrap-top .icon-tell-box .icon {
  margin-right: 29px;
}
.wrap-top .icon-tell-box .icon::after {
  content: "";
  width: 1px;
  height: 30px;
  background-color: #d9d9d9;
  top: 14px;
  position: absolute;
  margin-left: 14px;
}
.wrap-top .logo-box {
  margin-left: 152px;
}

.header-top {
  position: relative;
  padding: 18px 0px;
  background-color: #161e2d;
}

.header-top_nav {
  position: relative;
}

.header-top_nav a {
  position: relative;
  font-weight: 500;
  font-size: 16px;
  margin-right: 15px;
  padding-right: 15px;
  color: rgba(255, 255, 255, 0.7);
}

.header-top_nav a::before {
  position: absolute;
  content: "/";
  right: -4px;
  top: 0px;
}

.header-top_nav a:last-child {
  margin-right: 0;
  padding-right: 0;
}

.header-top_nav a:last-child::before {
  display: none;
}

.header-top_nav a:hover {
  color: white;
}

#showlogo {
  display: none;
}

/* Header Email */
.header-top_email {
  position: relative;
  font-weight: 500;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
}

.header-top_email span {
  position: relative;
  color: white;
}

.header-top_social {
  position: relative;
  margin-left: 60px;
}

.header-top_social a {
  position: relative;
  font-weight: 500;
  font-size: 16px;
  margin-left: 5px;
  color: rgba(255, 255, 255, 0.7);
}

.header-top_social a:hover {
  color: white;
}

.main-header .header-upper {
  position: relative;
}

.main-header .main-box {
  position: relative;
  padding: 0px 0px;
  left: 0px;
  top: 0px;
  width: 100%;
  background: none;
  transition: all 300ms ease;
}

.main-header .main-box .outer-container {
  position: relative;
}

.main-header .logo-box {
  position: relative;
  z-index: 10;
}

.main-header .logo-box .logo img {
  display: inline-block;
  max-width: 100%;
  transition: all 300ms ease;
}

.main-header .logo-box .logo {
  position: relative;
}

.main-header .header-lower {
  position: relative;
  transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -webkit-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
}

.main-header .header-lower .nav-outer {
  position: relative;
}

.main-header .header-upper .logo-box {
  position: relative;
  padding: 10px 0px 10px;
}

.main-header .header-upper .logo-box .logo {
  position: relative;
}

/* Main Menu */
.main-header .main-menu {
  position: relative;
  transition: all 300ms ease;
}

.main-header .main-menu .navbar-collapse {
  padding: 0;
  display: block;
}

.main-header .header-lower .main-menu .navigation {
  position: relative;
}

.main-header .main-menu .navigation > li {
  position: relative;
  display: inline-block;
  transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -webkit-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
  padding-inline-end: 55px;
}

.main-header .sticky-header .nav-outer .options-box {
  margin-top: 40px;
}

/*Sticky Header*/
.main-header .sticky-header {
  position: fixed;
  opacity: 0;
  visibility: hidden;
  left: 0px;
  top: 0px;
  width: 100%;
  padding: 0px 0px;
  z-index: 0;
  transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -webkit-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
}

.main-header .sticky-header .mobile-nav-toggler {
  display: none;
  cursor: pointer;
}

.main-header .sticky-header .logo {
  position: relative;
  padding: 7px 0px;
}

.main-header.fixed-header .sticky-header {
  z-index: 999;
  opacity: 1;
  visibility: visible;
  -op-animation-name: fadeInDown;
  animation-name: fadeInDown;
  -op-animation-duration: 500ms;
  animation-duration: 500ms;
  -op-animation-timing-function: linear;
  animation-timing-function: linear;
  -op-animation-iteration-count: 1;
  animation-iteration-count: 1;
}

.main-header .main-menu .navigation > li > a {
  font-family: var(--heading-font);
  position: relative;
  display: block;
  text-align: center;
  line-height: 26px;
  font-weight: 600;
  padding: 26px 0;
  letter-spacing: 0;
  color: #161e2d;
  font-size: 16px;
  transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -webkit-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
}
.main-header .main-menu .navigation > li > a::before {
  content: "";
  width: 0;
  height: 2px;
  bottom: 25px;
  position: absolute;
  left: auto;
  right: 0;
  z-index: 1;
  transition: width 0.6s cubic-bezier(0.25, 0.8, 0.25, 1) 0s;
  background: var(--primary-color);
}

.main-header.header-style-2 .main-menu .navigation > li > a {
  color: #ffffff;
}

.main-header .main-menu .navigation > li:last-child > a {
  padding-right: 0;
}

.main-header .sticky-header .main-menu .navigation > li {
  position: relative;
}

.main-header .sticky-header .main-menu .navigation > li:last-child {
  margin-right: 0;
}

.main-header .main-menu .navigation > li:last-child ul {
  right: 0px;
}

.main-header .sticky-header .main-menu .navigation > li > a:after {
  top: 22px;
}

.main-header .sticky-header .main-menu .navigation > li > a > span {
  top: 10px;
}

.main-header .main-menu .navigation > li:hover > a,
.main-header .main-menu .navigation > li.current > a {
  opacity: 1;
}
.main-header .main-menu .navigation > li:hover > a::before,
.main-header .main-menu .navigation > li.current > a::before {
  width: 100%;
  left: 0;
  right: auto;
}

.main-header.header-style-2 .main-menu .navigation > li:hover > a,
.main-header.header-style-2 .main-menu .navigation > li.current > a {
  color: #ffffff;
}

.main-header .main-menu .navigation > li > ul {
  position: absolute;
  left: -24px;
  width: 280px;
  z-index: 1;
  opacity: 0;
  visibility: hidden;
  transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  -webkit-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  background-color: #ffffff;
  border: 1px solid #e4e4e4;
  border-top: 0;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
  pointer-events: none;
}

.main-header .main-menu .navigation > .home > ul {
  width: 204px;
}

.main-header .main-menu .navigation > li > ul.from-right {
  left: auto;
  right: 0px;
}

.main-header .main-menu .navigation > li > ul > li {
  position: relative;
  width: 100%;
  text-align: start;
  transform: translateY(10px);
  transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -webkit-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
}

.main-header .main-menu .navigation > li > ul > li:last-child {
  margin-bottom: 0;
}

.main-header .main-menu .navigation > li:hover > ul > li {
  opacity: 1;
  transform: translateY(0px);
  transition-delay: 70ms;
}

.main-header .main-menu .navigation > li > ul > li:last-child > a {
  border-bottom: none;
}

.main-header .main-menu .navigation > li > ul > li > a {
  font-family: var(--heading-font);
  position: relative;
  padding: 16px 24px;
  display: block;
  line-height: 26px;
  font-weight: 600;
  font-size: 16px;
  color: #5c6368;
  transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -webkit-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
  border-bottom: 1px solid #e4e4e4;
}

.main-header .main-menu .navigation > li > ul > li > a:before {
  position: absolute;
  content: "\ea3d";
  width: 16px;
  height: 10px;
  opacity: 0;
  font-size: 12px;
  inset-inline-start: 24px;
  top: 16px;
  display: inline-block;
  transition: all 500ms ease;
  font-family: "icomoon";
  color: var(--primary-color);
}

.main-header .main-menu .navigation > li > ul > li:hover > a {
  color: var(--primary-color);
  padding-inline-start: 45px;
}

.main-header .main-menu .navigation > li > ul > li:hover > a::before {
  opacity: 1;
}

.main-header .main-menu .navigation > li > ul > li.dropdown2 > a:after {
  font-family: "icomoon";
  content: "\e91d";
  position: absolute;
  right: 24px;
  top: 16px;
  width: 10px;
  height: 20px;
  display: block;
  line-height: 24px;
  font-size: 12px;
  font-weight: 700;
  text-align: center;
  z-index: 5;
}

.main-header .main-menu .navigation > li > ul > li.dropdown2:hover > a:after {
  color: var(--primary-color);
}

.main-header .main-menu .navigation > li > ul > li > ul {
  position: absolute;
  left: 100%;
  top: 0px;
  width: 280px;
  opacity: 0;
  visibility: hidden;
  visibility: hidden;
  transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  -webkit-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  background-color: #ffffff;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
  border: 1px solid #e4e4e4;
  overflow: hidden;
}

.main-header .main-menu .navigation > li > ul > li > ul.from-right {
  left: auto;
  right: 0px;
}

.main-header .main-menu .navigation > li > ul > li > ul > li {
  position: relative;
  width: 100%;
  text-align: left;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  opacity: 0;
  transform: translateY(-8px);
  transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -webkit-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
}

.main-header .main-menu .navigation > li > ul > li > ul > li:last-child {
  margin-bottom: 0;
}

.main-header .main-menu .navigation > li > ul > li.dropdown2:hover > ul > li {
  opacity: 1;
  transform: translateY(0px);
  transition-delay: 70ms;
}

.main-header .main-menu .navigation > li > ul > li.dropdown2:hover > ul > li:nth-child(2) {
  transition-delay: 140ms;
}

.main-header .main-menu .navigation > li > ul > li.dropdown2:hover > ul > li:nth-child(3) {
  transition-delay: 210ms;
}

.main-header .main-menu .navigation > li > ul > li.dropdown2:hover > ul > li:nth-child(4) {
  transition-delay: 280ms;
}

.main-header .main-menu .navigation > li > ul > li.dropdown2:hover > ul > li:nth-child(5) {
  transition-delay: 350ms;
}

.main-header .main-menu .navigation > li > ul > li.dropdown2:hover > ul > li:nth-child(6) {
  transition-delay: 420ms;
}

.main-header .main-menu .navigation > li > ul > li.dropdown2:hover > ul > li:nth-child(7) {
  transition-delay: 490ms;
}

.main-header .main-menu .navigation > li > ul > li.dropdown2:hover > ul > li:nth-child(8) {
  transition-delay: 560ms;
}

.main-header .main-menu .navigation > li > ul > li > ul > li:last-child a {
  border-bottom: none;
}

.main-header .main-menu .navigation > li > ul > li > ul > li > a {
  position: relative;
  padding: 16px 24px;
  display: block;
  line-height: 24px;
  font-weight: 700;
  font-size: 16px;
  color: #5c6368;
  transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -webkit-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
  border-bottom: 1px solid #e4e4e4;
}

.main-header .main-menu .navigation > li > ul > li > ul > li > a:before {
  position: absolute;
  content: "\ea3d";
  width: 16px;
  height: 10px;
  opacity: 0;
  font-size: 12px;
  inset-inline-start: 24px;
  top: 16px;
  display: inline-block;
  transition: all 500ms ease;
  font-family: "icomoon";
  color: var(--primary-color);
}

.main-header .main-menu .navigation > li > ul > li > ul > li > a:hover::before {
  opacity: 1;
}

.main-header .main-menu .navigation > li > ul > .current > a {
  color: var(--primary-color);
}

.main-header .main-menu .navigation > li > ul > li > ul > .current > a {
  color: var(--primary-color);
}

.main-header .main-menu .navigation > li > ul > li > ul > li:hover > a {
  color: var(--primary-color);
  background-color: #ffffff;
}

.main-header .main-menu .navigation > li > ul > li > ul > li:hover > a {
  color: var(--primary-color);
  padding-left: 45px;
}

.main-header .main-menu .navigation > li.dropdown2:hover > ul {
  transform: scaleY(1);
  opacity: 1;
  visibility: visible;
  pointer-events: all;
}

.main-header .main-menu .navigation li > ul > li.dropdown2:hover > ul {
  transform: scaleY(1);
  transform: translateY(0px);
  opacity: 1;
  visibility: visible;
}

.main-header .main-menu .navigation > li.dropdown2 > a::after {
  content: "\e904";
  font-family: "icomoon";
  font-weight: 600;
  font-size: 16px;
  vertical-align: bottom;
  position: absolute;
  inset-inline-end: -20px;
  top: 50%;
  transform: translateY(-50%);
}

.main-header .main-menu .navbar-collapse > ul li.dropdown2 .dropdown2-btn {
  position: absolute;
  right: 10px;
  top: 6px;
  width: 30px;
  height: 30px;
  text-align: center;
  color: #ffffff;
  line-height: 28px;
  border: 1px solid var(--white-color-opicity-two);
  background-size: 20px;
  cursor: pointer;
  z-index: 5;
  display: none;
}

/***

====================================================================
	Mobile Menu
====================================================================

***/
.close-btn {
  position: fixed;
  inset-inline-end: 20px;
  top: 18px;
  line-height: 30px;
  width: 30px;
  text-align: center;
  font-size: 16px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 9999999999;
  opacity: 0;
  visibility: hidden;
}
.close-btn svg {
  width: 2.5rem;
  height: 2.5rem;
  stroke-width: 2;
}

.mobile-menu-visible .close-btn {
  visibility: visible;
  opacity: 1;
}

.mobile-menu .close-btn:hover {
  opacity: 0.5;
}

.mobile-menu .navigation {
  position: relative;
  display: block;
  width: 100%;
}
.mobile-menu .navigation li {
  position: relative;
  display: block;
}

.mobile-menu .navigation li {
  position: relative;
  display: block;
}
.mobile-menu .navigation li > ul > li:last-child {
  border-bottom: none;
}
.mobile-menu .navigation li.dropdown2 .dropdown2-btn {
  position: absolute;
  right: 0px;
  top: 0px;
  width: 100%;
  text-align: end;
  line-height: 44px;
  cursor: pointer;
  z-index: 5;
}
.mobile-menu .navigation li.dropdown2 .dropdown2-btn::before {
  content: "\e904";
  font-family: "icomoon";
  font-size: 12px;
  font-weight: 600;
  color: #161e2d;
  width: 20px;
  height: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  transform: rotate(-90deg);
}
.mobile-menu .navigation li.open .dropdown2-btn::before {
  transform: unset;
}

.mobile-menu .navigation li.dropdown2 > ul li a {
  border-bottom: unset;
  color: #161e2d;
}

.mobile-menu .navigation li.dropdown2 .dropdown2-btn span {
  opacity: 0;
}

.mobile-menu .navigation li > ul,
.mobile-menu .navigation li > ul > li > ul {
  display: none;
  background: rgba(237, 32, 39, 0.1);
  padding: 15px 20px;
  border-radius: 10px;
}

.mobile-menu .navigation li > ul > li,
.mobile-menu .navigation li > ul > li > ul > li {
  margin-bottom: 0;
}

.mobile-menu .navigation li > ul > li > a {
  padding: 7px 0px !important;
  font-weight: 500;
}

.mobile-menu .navigation li > ul > li > ul {
  padding-left: 15px;
}

.mobile-menu .navigation li > ul > li > ul > li > a {
  padding-left: 40px;
}

.mobile-menu .close-btn:hover {
  opacity: 0.5;
}

.mobile-menu .navigation li > ul > li:last-child {
  border-bottom: none;
}

.mobile-menu .navigation li > a {
  font-family: var(--heading-font);
  position: relative;
  display: block;
  font-weight: 600;
  line-height: 26px;
  padding: 10px 0px;
  font-size: 16px;
  color: #161e2d;
}

.mobile-menu .navigation li:hover > a,
.mobile-menu .navigation li.current > a,
.mobile-menu .navigation li.current li.current > a {
  color: var(--primary-color);
}

.main-header .sticky-header .navbar-header {
  display: none;
}

.main-header .outer-box {
  position: relative;
}

.main-header .sticky-header .main-menu .navigation > li > a {
  padding: 22px 0px;
}

.mobile-button {
  display: none;
  background-color: transparent;
  cursor: pointer;
  transition: all 0s ease;
}
.mobile-button::before, .mobile-button::after,
.mobile-button svg {
  color: var(--primary-color);
  transition: all ease 0.3s;
  stroke-width: 2;
  width: 2rem;
  height: 2rem;
}
.mobile-button::before {
  transform: translate3d(0, -9px, 0);
}
.mobile-button::after {
  transform: translate3d(0, 9px, 0);
}
.mobile-button.active span {
  opacity: 0;
}
.mobile-button.active::before {
  transform: rotate3d(0, 0, 1, 45deg);
}
.mobile-button.active::after {
  transform: rotate3d(0, 0, 1, -45deg);
}
.mobile-button.mobi-style::before, .mobile-button.mobi-style::after,
.mobile-button.mobi-style span {
  background-color: #ffffff;
}

.mobile-menu .login-box {
  border-bottom: 1px solid #e4e4e4;
  padding-bottom: 20px;
  margin-bottom: 20px;
}
.mobile-menu .login-box svg {
  margin-right: 5px;
}
.mobile-menu .login-box a,
.mobile-menu .login-box span {
  font-family: var(--heading-font);
  font-weight: 600;
}
.mobile-menu .menu-outer {
  border-bottom: 1px solid #e4e4e4;
  padding-bottom: 20px;
  margin-bottom: 20px;
}
.mobile-menu .button-mobi-sell {
  margin-bottom: 15px;
  border-bottom: 1px solid #e4e4e4;
  padding-bottom: 20px;
}
.mobile-menu .button-mobi-sell a {
  width: 100%;
}
.mobile-menu .mobi-icon-box .box {
  margin-bottom: 19px;
  border-bottom: 1px solid #e4e4e4;
  padding-bottom: 19px;
}
.mobile-menu .mobi-icon-box .box:last-child {
  border-bottom: 0;
  padding-bottom: 0px;
  margin-bottom: 0px;
}
.mobile-menu .mobi-icon-box .box .content {
  color: #a3abb0;
}
.mobile-menu .mobi-icon-box .box .content h5 {
  color: #5c6368;
}
.mobile-menu .mobi-icon-box .icon {
  margin-right: 10px;
  font-size: 20px;
}

.main-header .mobile-menu .menu-box .mCSB_scrollTools {
  width: 3px;
}

.dashboard {
  background-color: #f7f7f7;
}
.dashboard .main-header .main-menu {
  margin-left: 780px;
}
.dashboard .avatars-box {
  margin-right: 31px;
}
.dashboard .avatars-box .images {
  margin-right: 11px;
  height: 34px;
  width: 34px;
  overflow: hidden;
  border-radius: 50%;
}
.dashboard .avatars-box .title-avatar a::after {
  content: "\f078";
  font-family: "Font Awesome 5 Pro";
  font-weight: 900;
  font-size: 11px;
  margin-left: 8px;
  vertical-align: bottom;
}

.header-style-3 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.header-style-3 .header-lower {
  width: 100%;
}
.header-style-3 .nav-outer {
  padding-left: 190px;
}
.header-style-3 .outer-search {
  width: 504px;
  height: 48px;
  padding: 10px 6px;
  padding-left: 0px;
  border: 1px solid #e4e4e4;
  border-radius: 8px;
  display: flex;
  align-items: center;
}
.header-style-3 .outer-search .form-box {
  position: relative;
}
.header-style-3 .outer-search .form-box::after {
  content: "";
  position: absolute;
  right: 0;
  width: 1px;
  height: 20px;
  top: 50%;
  transform: translateY(-50%);
  background: #e4e4e4;
}
.header-style-3 .outer-search .form-box.box-1 {
  width: 152px;
}
.header-style-3 .outer-search .form-box.box-2 {
  width: 166px;
}
.header-style-3 .outer-search .form-box.box-3 {
  width: 142px;
}
.header-style-3 .outer-search .form-box.box-3::after {
  content: none;
}
.header-style-3 .outer-search input {
  padding: 0px 16px;
  font-size: 18px;
  line-height: 28px;
  color: #a3abb0;
  border: 0;
  border-radius: 0;
}
.header-style-3 .outer-search input::-moz-placeholder {
  color: #a3abb0;
}
.header-style-3 .outer-search input::placeholder {
  color: #a3abb0;
}
.header-style-3 .btn-search {
  flex-shrink: 0;
  width: 36px;
  height: 36px;
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0;
  border-radius: 8px;
  transition: all 0.3s ease;
}
.header-style-3 .btn-search:hover {
  background-color: var(--hover-color);
}
.header-style-3 .btn-search .icon {
  font-size: 20px;
  color: #ffffff;
}
.header-style-3 .btn-search-mb {
  display: none;
}
.header-style-3 .btn-menu-nav {
  font-size: 28px;
  margin-left: 20px;
  cursor: pointer;
}

.canvas-menu .menu-outer {
  border-bottom: 1px solid var(--main-header-border-color);
  padding-bottom: 20px;
  margin-bottom: 20px;
}
.canvas-menu .menu-outer .nav-link {
  font-family: var(--heading-font);
  padding: 10px 0px;
  font-size: 16px;
  line-height: 26px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
}
.canvas-menu .menu-outer .nav-link .icon {
  font-size: 16px;
  font-weight: 700;
  transform: rotate(-90deg);
  transition: all 0.3s ease;
}
.canvas-menu .menu-outer .nav-link:not(.collapsed) .icon {
  transform: unset;
}
.canvas-menu .menu-outer .nav-link:focus-visible {
  box-shadow: none;
}
.canvas-menu .menu-outer .nav-link.current, .canvas-menu .menu-outer .nav-link:hover {
  color: var(--primary-color);
}
.canvas-menu .menu-outer .nav-link.current .icon, .canvas-menu .menu-outer .nav-link:hover .icon {
  color: var(--primary-color);
}
.canvas-menu .menu-outer .sub-nav-menu {
  background: rgba(237, 32, 39, 0.1);
  padding: 15px 20px;
  border-radius: 10px;
}
.canvas-menu .menu-outer .sub-nav-menu .sub-nav-link {
  padding: 7px 0px;
  display: block;
  font-family: var(--heading-font);
  font-weight: 500;
  color: #5c6368;
}
.canvas-menu .menu-outer .sub-nav-menu .sub-nav-link.current, .canvas-menu .menu-outer .sub-nav-menu .sub-nav-link:hover {
  color: var(--primary-color);
}
.canvas-menu .box-btn {
  margin-bottom: 15px;
  border-bottom: 1px solid #e4e4e4;
  padding-bottom: 20px;
}
.canvas-menu .box-btn .tf-btn {
  width: 100%;
}
.canvas-menu .menu-icon-box .box .icon {
  margin-right: 10px;
  font-size: 20px;
}
.canvas-menu .menu-icon-box .box:not(:last-child) {
  margin-bottom: 19px;
  border-bottom: 1px solid #e4e4e4;
  padding-bottom: 19px;
}

/*------------ footer ---------------- */
/* Footer
------------------------------------------ */
.footer {
  padding-top: 60px;
  background-size: cover;
  background-repeat: no-repeat;
}
.footer .top-footer .content-footer-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  flex-wrap: wrap;
  padding-bottom: 29px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.footer .inner-footer {
  padding: 40px 0px 38px;
}
.footer .inner-footer .navigation-menu-footer li {
  margin-top: 4px;
}
.footer .bottom-footer {
  padding: 10px 0px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.footer .content-footer-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
}
.footer .content-footer-bottom .copyright {
  color: #a3abb0;
  font-size: 14px;
  line-height: 22px;
  font-weight: 400;
}
.footer .content-footer-bottom .menu-bottom {
  display: flex;
  align-items: center;
  gap: 19px;
}
.footer .content-footer-bottom .menu-bottom a {
  color: #a3abb0;
  font-size: 14px;
  line-height: 22px;
  font-weight: 400;
}
.footer .content-footer-bottom .menu-bottom a:hover {
  color: var(--primary-color);
}

.footer-cl-1 {
  margin-right: 20.4%;
}

.footer-cl-2 {
  margin-left: 15.4%;
}

.footer-cl-3 {
  margin-left: 20%;
}

.footer-cl-4 {
  margin-left: 17.3%;
}

.list-social {
  flex-wrap: wrap;
  gap: 12px;
}

.footer .wd-social {
  display: flex;
  align-items: center;
  gap: 16px;
}

.footer .wd-social span {
  font-family: var(--heading-font);
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
}

.subscribe-form {
  position: relative;
}
.subscribe-form .icon-left {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20px;
  color: #a3abb0;
}
.subscribe-form .invalid-feedback {
  position: absolute;
}

.footer-cl-4 .subscribe-form input {
  background: transparent;
  padding: 9px 70px 9px 28px;
  border-color: transparent;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #a3abb0;
  font-style: unset;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0;
}
.footer-cl-4 .subscribe-form input:focus {
  border-color: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.subscribe-form input::-moz-placeholder {
  font-size: 16px;
  line-height: 26px;
  color: #a3abb0;
  font-style: unset;
}

.subscribe-form input::placeholder {
  font-size: 16px;
  line-height: 26px;
  color: #a3abb0;
  font-style: unset;
}

.subscribe-form button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-end: 0;
  background: transparent;
  border-radius: 0px 8px 8px 0px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0px 10px 17px;
  border: 0;
}
.subscribe-form button .icon {
  font-size: 20px;
  color: #a3abb0;
}

.navigation-menu-footer li a {
  position: relative;
}

.navigation-menu-footer li a::after {
  content: "";
  width: 0;
  height: 1px;
  bottom: 0px;
  position: absolute;
  left: auto;
  right: 0;
  z-index: 1;
  transition: width 0.6s cubic-bezier(0.25, 0.8, 0.25, 1) 0s;
  background: var(--primary-color);
}

.navigation-menu-footer li a:hover {
  color: var(--primary-color);
}

.navigation-menu-footer li a:hover::after {
  width: 100%;
  left: 0;
  right: auto;
}

.ft-title {
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  margin-bottom: 15px;
}

.ft-download li:not(:last-child) {
  margin-bottom: 10px;
}

/*------------ tabs ---------------- */
.flat-tab-form .nav-tab-form {
  display: flex;
  align-items: center;
  gap: 4px;
}
.flat-tab-form .nav-tab-form .nav-link-item {
  padding: 15px 28px;
  border-radius: 8px 8px 0px 0px;
  font-size: 16px;
  line-height: 26px;
  letter-spacing: 0.8px;
  text-align: center;
  font-weight: 700;
}
.flat-tab-form .nav-tab-form.style-1 .nav-link-item {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}
.flat-tab-form .nav-tab-form.style-1 .nav-link-item.active, .flat-tab-form .nav-tab-form.style-1 .nav-link-item:hover {
  background-color: #ffffff;
  color: #161e2d;
}
.flat-tab-form .nav-tab-form.style-2 .nav-link-item {
  background-color: #e4e4e4;
  color: #161e2d;
}
.flat-tab-form .nav-tab-form.style-2 .nav-link-item.active, .flat-tab-form .nav-tab-form.style-2 .nav-link-item:hover {
  background-color: #ffffff;
  color: #161e2d;
}
.flat-tab-form .nav-tab-form.style-3 .nav-link-item {
  background-color: #ffffff;
  color: #a3abb0;
}
.flat-tab-form .nav-tab-form.style-3 .nav-link-item.active, .flat-tab-form .nav-tab-form.style-3 .nav-link-item:hover {
  background-color: var(--primary-color);
  color: #ffffff;
}
.flat-tab-form .nav-tab-form.style-4 {
  padding: 11px 0px;
  gap: 40px;
}
.flat-tab-form .nav-tab-form.style-4 .nav-link-item {
  background-color: transparent;
  border-bottom: 2px solid transparent;
  color: #a3abb0;
  padding: 2px 0px;
}
.flat-tab-form .nav-tab-form.style-4 .nav-link-item.active, .flat-tab-form .nav-tab-form.style-4 .nav-link-item:hover {
  color: #161e2d;
  border-color: #161e2d;
}

.flat-tab-recommended .nav-tab-recommended {
  margin-bottom: 40px;
  margin-top: 32px;
}

.nav-tab-recommended {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}
.nav-tab-recommended .nav-link-item {
  font-family: var(--heading-font);
  font-size: 16px;
  line-height: 26px;
  font-weight: 600;
  background-color: #f7f7f7;
  border-radius: 4px;
  padding: 8px 24px;
}
.nav-tab-recommended .nav-link-item.active, .nav-tab-recommended .nav-link-item:hover {
  background-color: var(--primary-color);
  color: #ffffff;
}

.nav-tab-privacy {
  border-left: 1px solid #e4e4e4;
}
.nav-tab-privacy .nav-link-item {
  font-size: 24px;
  line-height: 30px;
  font-weight: 700;
  padding: 10px 0px 8px 16px;
  margin-bottom: 40px;
  position: relative;
}
.nav-tab-privacy .nav-link-item::before {
  position: absolute;
  content: "";
  inset-inline-start: 0;
  width: 3px;
  top: 0;
  bottom: 0;
  background-color: var(--primary-color);
  opacity: 0;
  transition: all 0.3s ease;
}
.nav-tab-privacy .nav-link-item.active::before, .nav-tab-privacy .nav-link-item:hover::before {
  opacity: 1;
}
.nav-tab-privacy .nav-tab-item:last-child .nav-link-item {
  margin-bottom: 0;
}

.nav-tab-filter {
  display: flex;
  align-items: center;
  gap: 12px;
}
.nav-tab-filter .nav-link-item {
  all: unset;
  cursor: pointer;
}
.nav-tab-filter .nav-link-item .icon {
  width: 32px;
  height: 32px;
  color: #a3abb0;
  transition: all 0.3s ease;
}
.nav-tab-filter .nav-link-item.active .icon {
  color: #161e2d;
}

/*------------ slider banner ---------------- */
.flat-slider {
  position: relative;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  z-index: 123;
}
.flat-slider.home-1 {
  background-image: url(../images/slider/slider-1.jpg);
  background-attachment: fixed;
}
.flat-slider.home-1 .overlay {
  background: #161e2d;
  opacity: 0.6;
}
.flat-slider.home-1 .slider-content {
  position: relative;
  z-index: 1;
  padding: 233px 0px;
}
.flat-slider.home-1 .slider-content .heading {
  margin-bottom: 40px;
}
.flat-slider.home-1 .slider-content .subtitle {
  margin-top: 20px;
  padding: 0px 280px;
}
.flat-slider .overlay {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.flat-slider.home-2 {
  background-color: #f7f7f7;
}
.flat-slider.home-2 .img-banner-left {
  position: absolute;
  bottom: 0;
  inset-inline-start: 0;
  animation: ani4 7s infinite ease-in-out alternate;
}
.flat-slider.home-2 .img-banner-right {
  position: absolute;
  inset-inline-start: 50%;
  bottom: 0;
  top: 0;
  inset-inline-end: 0;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}
.flat-slider.home-2 .slider-content {
  padding: 114px 0px;
  position: relative;
  z-index: 10;
}
.flat-slider.home-2 .slider-content .heading .title {
  padding-inline-end: 54%;
}
.flat-slider.home-2 .slider-content .heading .subtitle {
  padding-inline-end: 48%;
  margin-top: 12px;
  margin-bottom: 40px;
  color: #5c6368;
}
.flat-slider.home-2 .wrap-search-link {
  margin-top: 24px;
  display: flex;
  align-items: center;
  gap: 5px;
  flex-wrap: wrap;
}
.flat-slider.home-2 .wrap-search-link p,
.flat-slider.home-2 .wrap-search-link a {
  color: #5c6368;
}
.flat-slider.home-2 .wrap-search-link a {
  position: relative;
}
.flat-slider.home-2 .wrap-search-link a::after {
  content: "";
  width: 0;
  height: 1px;
  bottom: 0px;
  position: absolute;
  left: auto;
  right: 0;
  z-index: 1;
  transition: width 0.6s cubic-bezier(0.25, 0.8, 0.25, 1) 0s;
  background: #161e2d;
}
.flat-slider.home-2 .wrap-search-link a:hover, .flat-slider.home-2 .wrap-search-link a.current {
  color: #161e2d;
}
.flat-slider.home-2 .wrap-search-link a:hover::after, .flat-slider.home-2 .wrap-search-link a.current::after {
  width: 100%;
  left: 0;
  right: auto;
}
.flat-slider .slider-sw-home2,
.flat-slider .slider-home2 {
  height: 100%;
}
.flat-slider .slider-home2 img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.flat-slider.home-3 {
  background-attachment: fixed;
}
.flat-slider.home-3 .slider-content {
  padding-top: 184px;
  padding-bottom: 184px;
}
.flat-slider.home-3 .slider-content .heading {
  margin-bottom: 30px;
}
.flat-slider.home-3 .slider-content .heading .title {
  padding-inline-end: 52%;
}
.flat-slider.home-3 .slider-content .heading .subtitle {
  padding-inline-end: 44%;
  margin-top: 8px;
  color: #5c6368;
}
.flat-slider.home-3 .flat-tab-form {
  padding: 75px 0px;
  position: unset;
  z-index: 5;
}
.flat-slider.home-5 {
  margin-bottom: -6.5rem;
  z-index: unset;
}
.flat-slider.home-5 .box-img img {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  max-height: 53rem;
}
.flat-slider.home-5 .thumbs-swiper-column1 {
  position: absolute;
  inset-inline-end: 315px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  width: 60px;
  height: 300px;
  overflow: hidden;
}
.flat-slider.home-5 .thumbs-swiper-column1 .swiper-wrapper {
  width: 60px;
  flex-direction: column;
}
.flat-slider.home-5 .thumbs-swiper-column1 .swiper-wrapper .swiper-slide {
  width: 60px !important;
  display: grid;
  height: 60px !important;
  margin-bottom: 16px;
}
.flat-slider.home-5 .thumbs-swiper-column1 .swiper-wrapper .image-detail {
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid #e4e4e4;
  width: 60px;
  height: 60px;
  cursor: pointer;
  opacity: 0.4;
}
.flat-slider.home-5 .thumbs-swiper-column1 .swiper-wrapper .swiper-slide-thumb-active .image-detail {
  opacity: 1;
}
.flat-slider.home-5 .info-box {
  top: 50%;
  transform: translateY(-50%);
  position: absolute;
  z-index: 4;
}

.flat-tab-form {
  position: relative;
}

@keyframes waviy {
  0%, 40%, 100% {
    transform: translateY(0);
  }
  20% {
    transform: translateY(-20px);
  }
}
.js-letters {
  overflow-y: hidden;
}
.js-letters > * {
  display: inline-block;
  min-width: 0.21em;
  backface-visibility: hidden;
  animation: slide-up 0.6s both;
}
.js-letters > *:nth-child(1) {
  animation-delay: 0.05s;
}
.js-letters > *:nth-child(2) {
  animation-delay: 0.1s;
}
.js-letters > *:nth-child(3) {
  animation-delay: 0.15s;
}
.js-letters > *:nth-child(4) {
  animation-delay: 0.2s;
}
.js-letters > *:nth-child(5) {
  animation-delay: 0.25s;
}
.js-letters > *:nth-child(6) {
  animation-delay: 0.3s;
}
.js-letters > *:nth-child(7) {
  animation-delay: 0.35s;
}
.js-letters > *:nth-child(8) {
  animation-delay: 0.4s;
}
.js-letters > *:nth-child(9) {
  animation-delay: 0.45s;
}
.js-letters > *:nth-child(10) {
  animation-delay: 0.5s;
}
.js-letters > *:nth-child(11) {
  animation-delay: 0.55s;
}
.js-letters > *:nth-child(12) {
  animation-delay: 0.6s;
}
.js-letters > *:nth-child(13) {
  animation-delay: 0.65s;
}
.js-letters > *:nth-child(14) {
  animation-delay: 0.7s;
}
.js-letters > *:nth-child(15) {
  animation-delay: 0.75s;
}
.js-letters > *:nth-child(16) {
  animation-delay: 0.8s;
}
.js-letters > *:nth-child(17) {
  animation-delay: 0.85s;
}
.js-letters > *:nth-child(18) {
  animation-delay: 0.9s;
}
.js-letters > *:nth-child(19) {
  animation-delay: 0.95s;
}
.js-letters > *:nth-child(20) {
  animation-delay: 1s;
}
.js-letters > *:nth-child(21) {
  animation-delay: 1.05s;
}
.js-letters > *:nth-child(22) {
  animation-delay: 1.1s;
}
.js-letters > *:nth-child(23) {
  animation-delay: 1.15s;
}
.js-letters > *:nth-child(24) {
  animation-delay: 1.2s;
}
.js-letters > *:nth-child(25) {
  animation-delay: 1.25s;
}
.js-letters > *:nth-child(26) {
  animation-delay: 1.3s;
}
.js-letters > *:nth-child(27) {
  animation-delay: 1.35s;
}
.js-letters > *:nth-child(28) {
  animation-delay: 1.4s;
}
.js-letters > *:nth-child(29) {
  animation-delay: 1.45s;
}
.js-letters > *:nth-child(30) {
  animation-delay: 1.5s;
}
.js-letters > *:nth-child(31) {
  animation-delay: 1.55s;
}
.js-letters > *:nth-child(32) {
  animation-delay: 1.6s;
}
.js-letters > *:nth-child(33) {
  animation-delay: 1.65s;
}
.js-letters > *:nth-child(34) {
  animation-delay: 1.7s;
}
.js-letters > *:nth-child(35) {
  animation-delay: 1.75s;
}
.js-letters > *:nth-child(36) {
  animation-delay: 1.8s;
}
.js-letters > *:nth-child(37) {
  animation-delay: 1.85s;
}
.js-letters > *:nth-child(38) {
  animation-delay: 1.9s;
}
.js-letters > *:nth-child(39) {
  animation-delay: 1.95s;
}
.js-letters > *:nth-child(40) {
  animation-delay: 2s;
}

@keyframes slide-up {
  0% {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }
  80% {
    opacity: 1;
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}
@keyframes ani4 {
  0%, 100% {
    transform: translateX(0);
    transition: all 0.3s ease;
  }
  50% {
    transform: translateX(-20px);
    transition: all 0.3s ease;
  }
}
/*------------ button ---------------- */
.tf-btn {
  font-family: var(--heading-font);
  font-size: 16px;
  line-height: 26px;
  font-weight: 700;
  text-align: center;
  padding: 10px 20px;
  border-radius: 4px;
  background-color: #ffffff;
  color: #161e2d;
  border: 1px solid #161e2d;
  transition: all 0.3s ease;
  text-decoration: none;
  transition: all 0.3s ease;
}
.tf-btn:hover {
  background-color: var(--primary-color);
  color: #ffffff;
  border-color: var(--primary-color);
}
.tf-btn.primary {
  background-color: var(--primary-color);
  color: #ffffff;
  border-color: var(--primary-color);
}
.tf-btn.primary:hover {
  background-color: var(--hover-color);
}
.tf-btn.size-1 {
  padding: 11px 32px;
}
.tf-btn.size-2 {
  padding: 11px 40px;
}

.btn-read-more {
  font-size: 16px;
  line-height: 26px;
  font-weight: 700;
  color: #161e2d;
  border-bottom: 2px solid #161e2d;
  padding: 0px 0px 4px 0px;
  display: inline-block;
}

.tag {
  display: inline-block;
}

.hover-btn-view:hover .btn-view.style-1 .icon {
  animation: 0.3s link-icon linear;
}
.hover-btn-view:hover .btn-view.style-1 .text {
  color: #161e2d;
}
.hover-btn-view:hover .btn-view.style-1 .text::before {
  width: 100%;
  left: 0;
  right: auto;
}

.btn-view {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}
.btn-view .text {
  font-size: 16px;
  line-height: 26px;
  font-weight: 600;
  font-family: var(--heading-font);
  color: #161e2d;
  position: relative;
}
.btn-view .text::before {
  position: absolute;
  content: "";
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary-color);
}
.btn-view .icon {
  color: var(--primary-color);
}
.btn-view:hover .icon {
  animation: 0.3s link-icon linear;
}
.btn-view.style-1 .text {
  color: #5c6368;
}
.btn-view.style-1 .text::before {
  width: 0;
  left: auto;
  right: 0;
  transition: width 0.6s cubic-bezier(0.25, 0.8, 0.25, 1) 0s;
  background-color: #161e2d;
}

/*------------ range slider ---------------- */
.noUi-target,
.noUi-target * {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  touch-action: none;
  -moz-user-select: none;
  user-select: none;
  box-sizing: border-box;
  cursor: pointer;
}

.noUi-target {
  position: relative;
  direction: ltr;
}

.noUi-base {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
  background: #e4e4e4;
}

.noUi-origin {
  position: absolute;
  right: 0;
  top: 0;
  left: 0;
  bottom: 0;
}

.noUi-handle {
  position: relative;
  z-index: 1;
}

.noUi-stacking .noUi-handle {
  z-index: 10;
}

.noUi-state-tap .noUi-origin {
  transition: left 0.3s, top 0.3s;
}

.noUi-state-drag * {
  cursor: inherit !important;
}

.noUi-base,
.noUi-handle {
  transform: translate3d(0, 0, 0);
}

.noUi-horizontal {
  height: 4px;
}

.noUi-horizontal .noUi-handle {
  position: relative;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  left: 0px;
  top: 50%;
  transform: translateY(-50%);
  background-color: #ffffff;
  border: 2px solid #161e2d;
  cursor: pointer;
}

.caption {
  margin-bottom: 10px;
}

/* Styling; */
.noUi-background {
  background: #e4e4e4;
}

.noUi-connect {
  background: #161e2d;
  transition: background 450ms;
}

.noUi-origin {
  border-radius: 0px;
}

.noUi-target {
  width: 100%;
  padding-right: 24px;
}

/* Handles and cursors;
 */
.noUi-draggable {
  cursor: w-resize;
}

.noUi-vertical .noUi-draggable {
  cursor: n-resize;
}

.noUi-handle {
  cursor: default;
  box-sizing: border-box !important;
}

/* Disabled state; */
[disabled].noUi-connect,
[disabled] .noUi-connect {
  background: #b8b8b8;
}

[disabled].noUi-origin,
[disabled] .noUi-handle {
  cursor: not-allowed;
}

.slider-labels .caption {
  font-weight: 500;
  font-size: 16px;
}

/*------------ form ---------------- */
.form-control {
  border: 1px solid #e4e4e4;
  font-family: var(--primary-font);
  outline: 0;
  box-shadow: none;
  font-size: 16px;
  line-height: 26px;
  border-radius: 8px;
  padding: 14px 16px;
  width: 100%;
  background: #ffffff;
  color: #161e2d;
  font-weight: 400;
}
.form-control:focus {
  box-shadow: unset;
  border-color: var(--primary-color) !important;
}
.form-control.style-1 {
  padding: 10px 16px;
}

.ip-icon {
  position: relative;
}
.ip-icon .icon-right {
  position: absolute;
  right: 14px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
}

.tf-checkbox {
  position: relative;
  border: 1px solid #161e2d;
  background: none;
  cursor: pointer;
  outline: 0;
  width: 12px;
  height: 12px;
  border-radius: 2px;
  -webkit-appearance: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tf-checkbox::before {
  content: "\e93b";
  position: absolute;
  font-family: "icomoon";
  font-size: 8px;
  line-height: 12px;
  color: #ffffff;
}
.tf-checkbox.style-1 {
  width: 15px;
  height: 15px;
  border-color: #e4e4e4;
}
.tf-checkbox.style-1::before {
  font-size: 8px;
  line-height: 15px;
}
.tf-checkbox.primary {
  border-color: #a3abb0;
}
.tf-checkbox.primary:checked {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
}
.tf-checkbox.style-2 {
  border-color: #5c6368;
}
.tf-checkbox:checked {
  border-color: #161e2d;
  background-color: #161e2d;
}

.tf-radio {
  position: relative;
  border: 1px solid #a3abb0;
  border-radius: 50%;
  background: none;
  cursor: pointer;
  outline: 0;
  height: 16px;
  width: 16px;
  -webkit-appearance: none;
}
.tf-radio:checked {
  border-color: var(--primary-color);
}
.tf-radio:checked::before {
  opacity: 1;
}
.tf-radio::before {
  content: "";
  position: absolute;
  top: 2px;
  left: 2px;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  background-color: var(--primary-color);
  opacity: 0;
}

.search-box {
  position: relative;
  width: 100%;
}
.search-box .right-icon {
  all: unset;
  position: absolute;
  font-size: 20px;
  color: #161e2d;
  top: 50%;
  transform: translateY(-50%);
  right: 16px;
  cursor: pointer;
}
.search-box .right-icon svg {
  stroke-width: 1.5;
}
.search-box .search-field {
  padding: 14px 45px 14px 16px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  border: 1px solid #e4e4e4;
  color: #161e2d;
  background-color: #ffffff;
}
.search-box .search-field::-moz-placeholder {
  color: #a3abb0;
}
.search-box .search-field::placeholder {
  color: #a3abb0;
}
.search-box .search-field:focus {
  border-color: var(--primary-color);
}

.uploadfile .add-file {
  position: relative;
  overflow: hidden;
  display: inline-flex;
  background-color: var(--primary-color);
  border-radius: 8px;
  width: 48px;
  cursor: pointer;
}
.uploadfile .add-file::before {
  border-radius: 8px;
  position: absolute;
  width: 100%;
  height: 100%;
  padding: 4px 6px 4px 20px;
  font-weight: 400;
  font-size: 10px;
  line-height: 16px;
  content: "Add";
  display: flex;
  align-items: center;
  border: 1px solid var(--primary-color);
  background-color: var(--primary-color);
  color: #ffffff;
}
.uploadfile .add-file::after {
  position: absolute;
  content: "\e92d";
  font-family: "icomoon";
  color: #ffffff;
  font-size: 12px;
  left: 6px;
  top: 6px;
}

.wrap-form-comment .group-ip {
  display: grid;
  gap: 20px;
  grid-template-columns: 1fr 1fr;
}
.wrap-form-comment .form-wg {
  margin-top: 20px;
}
.wrap-form-comment textarea:disabled,
.wrap-form-comment button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.wrap-form-comment .sub-ip {
  color: #5c6368;
  margin-bottom: 8px;
}

.wd-find-select {
  display: flex;
  border-radius: 12px;
  background-color: #ffffff;
}
.wd-find-select .tf-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 11px 40px;
  border-radius: 0 4px 4px 0;
}
.wd-find-select .inner-group {
  padding: 20px 30px 20px 20px;
  flex-wrap: nowrap;
  display: flex;
  gap: 20px;
  flex-grow: 1;
}
.wd-find-select .inner-group .form-style {
  width: 100%;
  border-inline-end: 1px solid #e4e4e4;
}
.wd-find-select .inner-group .form-style .form-control,
.wd-find-select .inner-group .form-style .nice-select {
  border: 0;
  padding: 0;
  padding-top: 4px;
}
.wd-find-select .inner-group .form-style .form-control {
  color: #161e2d;
  font-weight: 600;
  font-size: 18px;
  line-height: 28px;
  padding-inline-end: 45px;
}
.wd-find-select .inner-group .form-style .form-control::-moz-placeholder {
  color: #161e2d;
}
.wd-find-select .inner-group .form-style .form-control::placeholder {
  color: #161e2d;
}
.wd-find-select .inner-group .form-style .nice-select {
  font-family: var(--heading-font);
  color: #161e2d;
  font-weight: 600;
  font-size: 18px;
  line-height: 28px;
}
.wd-find-select .inner-group .form-style .nice-select::after {
  right: 24px;
}
.wd-find-select .inner-group .form-style label {
  font-size: 14px;
  line-height: 24px;
  color: #5c6368;
}
.wd-find-select .inner-group .group-ip {
  position: relative;
}
.wd-find-select .inner-group .group-ip .icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-end: 20px;
  width: 20px;
  height: 20px;
}
.wd-find-select .inner-group .box-filter {
  width: -moz-max-content;
  width: max-content;
  flex-shrink: 0;
}
.wd-find-select .inner-group .box-filter .filter-advanced {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #161e2d;
  font-family: var(--heading-font);
  font-weight: 600;
}
.wd-find-select .inner-group .box-filter .icon {
  height: 28px;
  width: 28px;
}
.wd-find-select .group-select .nice-select {
  padding: 14px 17px;
  border-radius: 10px;
}
.wd-find-select.style-2 {
  padding: 20px;
  gap: 20px;
}
.wd-find-select.style-2 .inner-group {
  padding: 0px;
}
.wd-find-select.style-2 .inner-group .form-style label {
  font-weight: 700;
  color: #161e2d;
  letter-spacing: 0.8px;
}
.wd-find-select.style-2 .inner-group .form-style .form-control {
  color: #a3abb0;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  padding-inline-end: 45px;
}
.wd-find-select.style-2 .inner-group .form-style .form-control::-moz-placeholder {
  color: #a3abb0;
}
.wd-find-select.style-2 .inner-group .form-style .form-control::placeholder {
  color: #a3abb0;
}
.wd-find-select.style-2 .inner-group .form-style .nice-select {
  color: #161e2d;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
}
.wd-find-select.style-2 .inner-group .form-style .nice-select::after {
  right: 24px;
}
.wd-find-select.style-2 .inner-group .box-filter {
  display: flex;
}
.wd-find-select.style-2 .inner-group .box-filter .filter-advanced {
  flex-direction: row;
  gap: 4px;
}
.wd-find-select.style-2 .inner-group .box-filter .text-advanced {
  font-size: 14px;
  line-height: 24px;
  letter-spacing: 0.8px;
  color: #161e2d;
  font-weight: 700;
}
.wd-find-select.style-2 .tf-btn {
  border-radius: 4px !important;
}
.wd-find-select.shadow-st {
  box-shadow: 0px 10px 25px 0px rgba(54, 95, 104, 0.1);
}
.wd-find-select.no-left-round {
  border-top-left-radius: 0;
}
.wd-find-select.style-3 .nice-select,
.wd-find-select.style-3 .form-control {
  background-color: #f7f7f7;
}
.wd-find-select.style-3 .inner-group {
  border: 1px solid #e4e4e4;
  background-color: #f7f7f7;
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;
}

.wd-search-form {
  position: absolute;
  z-index: 10;
  width: 100%;
  background: #ffffff;
  border: 1px solid #e4e4e4;
  border-radius: 12px;
  padding: 28px 20px;
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden;
}
.wd-search-form.show {
  display: unset !important;
  opacity: 1;
  visibility: visible;
  margin-top: 5px;
}
.wd-search-form .group-box {
  gap: 80px;
}
.wd-search-form .group-price {
  margin-bottom: 26px;
}
.wd-search-form .box-title-price {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 22px;
}
.wd-search-form .box-title-price .title-price {
  font-size: 14px;
  line-height: 24px;
  color: #5c6368;
  font-weight: 500;
}
.wd-search-form .group-select {
  gap: 40px;
}
.wd-search-form .group-select .title-select {
  margin-bottom: 4px;
  font-size: 14px;
  line-height: 24px;
}
.wd-search-form .group-checkbox {
  margin-top: 20px;
}
.wd-search-form .group-checkbox .amenities-item {
  display: flex;
  align-items: center;
  gap: 6px;
}
.wd-search-form .group-checkbox .amenities-item .text-cb-amenities {
  color: #5c6368;
  cursor: pointer;
}
.wd-search-form .group-checkbox .amenities-item .tf-checkbox:checked ~ .text-cb-amenities {
  color: #161e2d;
}
.wd-search-form.style-2 {
  padding: 24px 30px 30px;
  border: 0;
}
.wd-search-form.style-2 .group-price .widget-price:not(:first-child) {
  margin-top: 30px;
}
.wd-search-form.style-2 .box {
  margin-bottom: 30px;
}
.wd-search-form.style-2 .box .title-select {
  font-size: 14px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 4px;
}
.wd-search-form.style-2 .box .box-select:not(:first-child) {
  margin-top: 20px;
}
.wd-search-form.style-2 .box .box-select .nice-select {
  padding: 0;
  border: 0;
  font-size: 18px;
  line-height: 28px;
  font-weight: 700;
}
.wd-search-form.style-2 .group-checkbox .amenities-item {
  margin-top: 12px;
}
.wd-search-form.style-2 .group-checkbox .amenities-item:first-child {
  margin-top: 0;
}
.wd-search-form .tf-btn {
  width: 100%;
}

.wd-filter-select {
  background-color: #ffffff;
  border-radius: 12px;
  border-top-left-radius: 0;
  padding: 24px 30px 30px;
}
.wd-filter-select .form-style:not(:first-child) {
  margin-top: 24px;
}
.wd-filter-select .form-style label {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 24px;
  font-weight: 700;
  letter-spacing: 0.8px;
  color: #161e2d;
}
.wd-filter-select .form-style .form-control,
.wd-filter-select .form-style .nice-select {
  font-size: 18px;
  line-height: 28px;
  padding: 10px 16px;
  padding-right: 40px;
}
.wd-filter-select .filter-advanced {
  display: flex;
  align-items: center;
  gap: 4px;
}
.wd-filter-select .filter-advanced .icon {
  width: 28px;
  height: 28px;
  color: #161e2d;
}
.wd-filter-select .tf-btn {
  width: 100%;
}

.flat-filter-form .wd-search-form {
  left: 15px;
  right: 15px;
  width: auto;
}
.flat-filter-form .wd-search-form.style-2 {
  height: 500px;
  overflow-y: auto;
  border: 1px solid #e4e4e4;
}
.flat-filter-form .wd-search-form.style-2::-webkit-scrollbar {
  width: 2px;
}
.flat-filter-form .wd-search-form.style-2::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 6px;
}

.widget-filter-search .title {
  margin-bottom: 20px;
}
.widget-filter-search .wd-filter-select {
  border-radius: 0;
  background-color: transparent;
  padding: 0;
  margin-top: 20px;
}
.widget-filter-search .wd-filter-select .form-style .title-select {
  font-size: 16px;
  line-height: 26px;
  font-weight: 400;
  color: #5c6368;
}
.widget-filter-search .wd-filter-select .form-style .form-control,
.widget-filter-search .wd-filter-select .form-style .nice-select {
  font-size: 16px;
  line-height: 26px;
}
.widget-filter-search .wd-filter-select .form-style:not(:first-child) {
  margin-top: 12px;
}
.widget-filter-search .wd-filter-select .form-style .group-checkbox .group-amenities {
  margin-top: 8px;
}
.widget-filter-search .wd-filter-select .form-style .group-checkbox .group-amenities .amenities-item {
  display: flex;
  gap: 4px;
  align-items: center;
}
.widget-filter-search .wd-filter-select .form-style .group-checkbox .group-amenities .amenities-item label {
  margin-bottom: 0;
}
.widget-filter-search .wd-filter-select .form-style .group-checkbox .group-amenities .amenities-item:not(:first-child) {
  margin-top: 12px;
}
.widget-filter-search .wd-filter-select .form-style .group-checkbox .group-amenities .text-cb-amenities {
  font-size: 16px;
  line-height: 26px;
  color: #5c6368;
  font-weight: 400;
  text-transform: unset;
  letter-spacing: unset;
}
.widget-filter-search .wd-filter-select .widget-price .box-title-price {
  display: flex;
  gap: 8px;
  margin-bottom: 18px;
}
.widget-filter-search .wd-filter-select .widget-price .title-price {
  color: #5c6368;
}
.widget-filter-search .wd-filter-select .widget-price.wd-price-2 {
  margin: 22px 0px;
}
.widget-filter-search .nav-tab-form {
  border-radius: 8px;
  gap: 0;
  overflow: hidden;
}
.widget-filter-search .nav-tab-form .nav-tab-item {
  flex-grow: 1;
}
.widget-filter-search .nav-tab-form .nav-tab-item .nav-link-item {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0;
  background-color: #ffffff;
  padding: 15px;
}
.widget-filter-search .nav-tab-form .nav-tab-item .nav-link-item.active {
  background-color: var(--primary-color);
  color: #ffffff;
}
.widget-filter-search .wd-amenities {
  display: none;
}
.widget-filter-search .inner-group.inner-filter .btn-hide-advanced {
  display: none;
}
.widget-filter-search .inner-group.inner-filter.active .btn-hide-advanced {
  display: block;
}
.widget-filter-search .inner-group.inner-filter.active .btn-show-advanced {
  display: none;
}
.widget-filter-search .inner-group.inner-filter-2 .btn-hide-advanced-2 {
  display: none;
}
.widget-filter-search .inner-group.inner-filter-2.active .btn-hide-advanced-2 {
  display: block;
}
.widget-filter-search .inner-group.inner-filter-2.active .btn-show-advanced-2 {
  display: none;
}
.widget-filter-search .inner-group .form-btn-fixed {
  position: fixed;
  z-index: 123;
  width: 23.3%;
  left: 0;
  bottom: 0;
  right: 0;
  padding: 16px 30px;
  background-color: #ffffff;
}
.widget-filter-search .inner-group .tf-btn {
  margin: 0;
  padding: 10px 20px;
}

.offcanvas.canvas-filter-mb {
  overflow: auto;
  max-width: 325px;
  background-color: #f7f7f7;
}
.offcanvas.canvas-filter-mb::-webkit-scrollbar {
  width: 0px;
}
.offcanvas.canvas-filter-mb::-webkit-scrollbar-thumb {
  background: transparent;
}
.offcanvas.canvas-filter-mb .nav-tab-form {
  height: 80px;
  border-bottom: 1px solid #e4e4e4;
  background-color: #ffffff;
}
.offcanvas.canvas-filter-mb .canvas-content {
  padding: 15px;
  background-color: #f7f7f7;
}
.offcanvas.canvas-filter-mb .wd-find-select {
  padding: 0;
  background-color: #f7f7f7;
}
.offcanvas.canvas-filter-mb .wd-find-select .inner-group {
  border: 0;
  border-radius: 0;
  display: block;
  padding: 0;
}
.offcanvas.canvas-filter-mb .wd-find-select .wd-show-filter-mb {
  display: none;
}
.offcanvas.canvas-filter-mb .wd-find-select .btn-show-advanced-mb {
  display: flex;
  align-items: center;
  gap: 8px;
}
.offcanvas.canvas-filter-mb .wd-find-select .btn-show-advanced-mb .icon {
  font-size: 20px;
}
.offcanvas.canvas-filter-mb .wd-find-select .btn-show-advanced-mb .text-advanced {
  font-weight: 600;
}
.offcanvas.canvas-filter-mb .wd-find-select .form-style {
  border: 0;
}
.offcanvas.canvas-filter-mb .wd-find-select .form-style:not(:last-child) {
  margin-bottom: 20px;
}
.offcanvas.canvas-filter-mb .wd-find-select .box-title-price {
  margin-bottom: 22px;
}
.offcanvas.canvas-filter-mb .wd-find-select .amenities-item {
  display: flex;
  gap: 8px;
}
.offcanvas.canvas-filter-mb .wd-find-select .amenities-item:not(:last-child) {
  margin-bottom: 8px;
}
.offcanvas.canvas-filter-mb .wd-find-select .amenities-item label {
  margin-bottom: 0;
}
.offcanvas.canvas-filter-mb .wd-find-select .group-amenities {
  margin-top: 20px;
}
.offcanvas.canvas-filter-mb .wd-find-select .title-price {
  font-size: 14px;
  line-height: 24px;
  color: #5c6368;
  font-weight: 500;
}

/*------------ nice select ---------------- */
.nice-select {
  -webkit-tap-highlight-color: transparent;
  background-color: #fff;
  border: 1px solid #e4e4e4;
  border-radius: 3px;
  padding: 9px 8px 9px 12px;
  box-sizing: border-box;
  clear: both;
  cursor: pointer;
  display: block;
  /* float: left; */
  font-family: var(--paraFont);
  font-size: 16px;
  line-height: 26px;
  font-weight: 400;
  /* text-transform: capitalize; */
  outline: none;
  position: relative;
  transition: all linear 0.2s;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  white-space: nowrap;
  width: 100%;
  border-radius: 8px;
  color: #161e2d;
}

.nice-select:active,
.nice-select.open,
.nice-select:focus {
  border-color: #e4e4e4;
}

.nice-select:after {
  border-bottom: 1.7px solid #161e2d;
  border-right: 1.7px solid #161e2d;
  content: "";
  height: 8px;
  width: 8px;
  margin-top: -6px;
  pointer-events: none;
  position: absolute;
  right: 18px;
  top: 50%;
  transform-origin: 66% 66%;
  transform: rotate(45deg);
  transition: all 0.15s ease-in-out;
}

.nice-select.open:after {
  transform: rotate(-135deg);
  -moz-transform: rotate(-135deg);
  -o-transform: rotate(-135deg);
}

.nice-select.open .list {
  opacity: 1;
  z-index: 10;
  pointer-events: auto;
  transform: scale(1) translateY(0);
  width: 100%;
  -moz-transform: scale(1) translateY(0);
  -o-transform: scale(1) translateY(0);
}

.nice-select.disabled {
  border-color: #ededed;
  color: #999;
  pointer-events: none;
}

.nice-select.disabled:after {
  border-color: #cccccc;
}

.nice-select.wide {
  width: 100%;
}

.nice-select.wide .list {
  left: 0 !important;
  right: 0 !important;
}

.nice-select.right {
  float: right;
}

.nice-select.right .list {
  left: auto;
  right: 0;
}

.nice-select.small {
  font-size: 12px;
  height: 36px;
  line-height: 34px;
}

.nice-select.small:after {
  height: 4px;
  width: 4px;
}

.nice-select.small .option {
  line-height: 34px;
  min-height: 34px;
}

.nice-select .list {
  background-color: #ffffff;
  border-radius: 5px;
  box-shadow: 0 0 0 1px rgba(68, 68, 68, 0.11);
  box-sizing: border-box;
  margin-top: 4px;
  opacity: 0;
  overflow: hidden;
  padding: 0;
  pointer-events: none;
  position: absolute;
  top: 100%;
  left: 0;
  transform-origin: 50% 0;
  transform: scale(0.75) translateY(-21px);
  transition: all 0.2s cubic-bezier(0.5, 0, 0, 1.25), opacity 0.15s ease-out;
  z-index: 9;
  width: 100%;
  font-size: 14px;
  max-height: 155px;
  overflow: auto;
}

.nice-select .list.style {
  max-height: unset;
}

.nice-select .list::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #f5f5f5;
  border-radius: 5px;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
}

.nice-select .list::-webkit-scrollbar-thumb {
  background-color: #a7a7a7;
  border-radius: 5px;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
}

.nice-select .list::-webkit-scrollbar {
  width: 6px;
  height: 4px;
  background-color: #f5f5f5;
  border-radius: 5px;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
}

.nice-select .option {
  cursor: pointer;
  font-weight: 500;
  line-height: 40px;
  list-style: none;
  min-height: 40px;
  outline: none;
  padding-left: 18px;
  padding-right: 29px;
  font-size: 16px;
  text-align: left;
  transition: all 0.2s;
  color: #161e2d;
}

.nice-select .option:hover,
.nice-select .option.focus,
.nice-select .option.selected.focus {
  background-color: #ffffff;
  color: var(--primary-color);
}

.nice-select .option.selected {
  font-weight: 600;
}

.nice-select .option.disabled {
  color: #161e2d;
  cursor: default;
}

.no-csspointerevents .nice-select .list {
  display: none;
}

.no-csspointerevents .nice-select.open .list {
  display: block;
}

.box-select-1 .nice-select {
  min-width: 120px;
  padding-right: 40px;
}

/*------------ carousel ---------------- */
.swiper:hover {
  cursor: pointer;
}

.swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0px 8px;
}

.sw-auto .swiper-slide {
  width: auto;
}

.box-navigation {
  display: flex;
  gap: 20px;
}
.box-navigation .navigation {
  width: 60px;
  height: 60px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #e4e4e4;
  background-color: #ffffff;
  border-radius: 8px;
  transition: all 0.3s ease;
}
.box-navigation .navigation .icon {
  color: #161e2d;
  width: 2.5rem;
  height: 2.5rem;
}
.box-navigation .navigation.swiper-button-disabled, .box-navigation .navigation:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}
.box-navigation .navigation.swiper-button-disabled .icon, .box-navigation .navigation:hover .icon {
  color: #ffffff;
}
.box-navigation .navigation.style-1 {
  width: 44px;
  height: 44px;
}
.box-navigation .navigation.style-1 .icon {
  font-size: 18px;
}

.sw-pagination .swiper-pagination-bullet {
  width: 8px;
  height: 8px;
  background-color: #a3abb0;
  opacity: 1;
  position: relative;
  transition: all 0.5s ease;
}
.sw-pagination .swiper-pagination-bullet::before {
  position: absolute;
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
  border-radius: 50%;
  content: "";
  width: 18px;
  height: 18px;
  border: 1px solid var(--primary-color);
  overflow: visible;
  opacity: 0;
  transition: all 0.3s ease;
}
.sw-pagination .swiper-pagination-bullet-active {
  background-color: var(--primary-color) !important;
}
.sw-pagination .swiper-pagination-bullet-active::before {
  opacity: 1;
}

/*------------ avatar ---------------- */
.avatar.round {
  border-radius: 50%;
  overflow: hidden;
}
.avatar img {
  width: 100%;
  min-width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.avt-40 {
  width: 40px;
  min-width: 40px;
  height: 40px;
}

.avt-56 {
  width: 56px;
  min-width: 56px;
  height: 56px;
}

.avt-60 {
  width: 60px;
  min-width: 60px;
  height: 60px;
}

.avt-100 {
  width: 100px;
  min-width: 100px;
  height: 100px;
}

/*------------ off canvas ---------------- */
.offcanvas {
  --bs-offcanvas-transition: transform 0.4s ease-in-out;
}
.offcanvas.canvas-menu {
  border-right: 0;
}
.offcanvas.canvas-menu .offcanvas-header {
  padding: 24px;
  box-shadow: 0px 4px 18px 0px rgba(0, 0, 0, 0.08);
  background-color: #ffffff;
}
.offcanvas.canvas-menu .off-canvas-body {
  padding: 30px 24px;
  overflow: auto;
}
.offcanvas.canvas-menu .off-canvas-body .login-box {
  border-bottom: 1px solid #e4e4e4;
  padding-bottom: 20px;
  margin-bottom: 20px;
}
.offcanvas.canvas-menu .off-canvas-body .login-box a,
.offcanvas.canvas-menu .off-canvas-body .login-box span {
  font-family: var(--heading-font);
  font-weight: 600;
}
.offcanvas.canvas-menu .off-canvas-body::-webkit-scrollbar {
  width: 0px;
}
.offcanvas.canvas-menu .off-canvas-body::-webkit-scrollbar-thumb {
  background: transparent;
}
.offcanvas.canvas-filter {
  height: 206px;
  border: 0;
}
.offcanvas.canvas-filter .main-header {
  border: 0;
}
.offcanvas.canvas-filter .tab-content {
  position: relative;
}

.offcanvas-backdrop {
  background-color: rgba(22, 30, 45, 0.3);
}
.offcanvas-backdrop.show {
  opacity: 1;
}

.icon-close-popup {
  width: 30px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #a3abb0;
  cursor: pointer;
}

.offcanvas#createProject {
  z-index: 1047;
}

.content-sidebarRight {
  overflow: auto;
}

/*------------ box icon ---------------- */
.box-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.box-icon.w-28 {
  width: 28px;
  height: 28px;
}
.box-icon.w-28 .icon {
  font-size: 14px;
}
.box-icon.w-32 {
  width: 32px;
  height: 32px;
}
.box-icon.w-40 {
  width: 40px;
  height: 40px;
}
.box-icon.w-52 {
  width: 52px;
  height: 52px;
}
.box-icon.w-60 {
  width: 60px;
  height: 60px;
}
.box-icon.w-68 {
  width: 68px;
  height: 68px;
}
.box-icon.w-80 {
  width: 80px;
  height: 80px;
}
.box-icon.w-80 .icon {
  width: 44px;
  height: 44px;
}
.box-icon.round {
  border-radius: 50%;
}
.box-icon.social {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}
.box-icon.social .icon {
  color: #ffffff;
  font-size: 18px;
}
.box-icon.social .icon.icon-youtube {
  font-size: 12px;
}
.box-icon.social.square {
  background-color: #f7f7f7;
  border-radius: 4px;
  border: 1px solid #e4e4e4;
}
.box-icon.social.square .icon {
  color: #161e2d;
  font-size: 18px;
}
.box-icon.social.square .icon.icon-youtube {
  font-size: 12px;
}
.box-icon.social.square:hover .icon {
  color: #ffffff;
}
.box-icon.social:hover {
  background-color: var(--primary-color);
  color: #ffffff;
  border-color: var(--primary-color);
}

/*------------ hover ---------------- */
.hover-img .img-style {
  overflow: hidden;
  border-radius: 12px;
  position: relative;
}
.hover-img .img-style img {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  transition: transform 0.3s cubic-bezier(0, 0, 0.44, 1.18);
}
.hover-img .img-style::after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5019607843);
  opacity: 0;
  transition: all 0.3s ease;
}
.hover-img:hover img {
  transform: scale(1.09);
}
.hover-img:hover .img-style::after {
  opacity: 1;
}
.hover-img.not-overlay .img-style::after {
  content: none;
}
.hover-img.not-overlay::after {
  content: none;
}
.hover-img .img-style2 {
  overflow: hidden;
  border-radius: 10px;
}
.hover-img .img-style2 .img-hv {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  transition: all 1s cubic-bezier(0.3, 1, 0.35, 1) 0s;
  transition: transform 500ms ease;
}

.hover-img2 .img-style2 {
  overflow: hidden;
  border-radius: 8px;
}
.hover-img2 .img-style2 .img2 {
  transition: all 0.3s ease;
}
.hover-img2:hover .img2 {
  transform: scale(1.1) rotate(3deg);
}

.hover-img3 .img-style3 {
  border-radius: 8px;
  overflow: hidden;
}
.hover-img3 .img-style3 img {
  width: 100%;
  transition: all 0.3s ease;
}
.hover-img3:hover img {
  transform: scale(1.075);
  transition: all 0.3s ease;
}

.pagi2 .swiper-pagination2:hover .box-img .icon-practice,
.swiper-button-next2:hover .box-img .icon-practice,
.swiper-button-prev2:hover .box-img .icon-practice,
.hv-one:hover .box-img .icon-practice {
  opacity: 1;
  z-index: 99;
  top: 50%;
  transition-delay: 0.5s;
}
.pagi2 .swiper-pagination2:hover .img-style::before,
.swiper-button-next2:hover .img-style::before,
.swiper-button-prev2:hover .img-style::before,
.hv-one:hover .img-style::before {
  opacity: 1;
}
.pagi2 .swiper-pagination2 .img-style,
.swiper-button-next2 .img-style,
.swiper-button-prev2 .img-style,
.hv-one .img-style {
  border-radius: 10px;
  overflow: hidden;
}
.pagi2 .swiper-pagination2 .img-style::before,
.swiper-button-next2 .img-style::before,
.swiper-button-prev2 .img-style::before,
.hv-one .img-style::before {
  content: "";
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  position: absolute;
  background: rgba(0, 0, 0, 0.5019607843);
  width: 100%;
  height: 100%;
  transition: all 0.5s ease;
  z-index: 99;
  opacity: 0;
  border-radius: 10px;
}
.pagi2 .swiper-pagination2 .img-style.s-one::before,
.swiper-button-next2 .img-style.s-one::before,
.swiper-button-prev2 .img-style.s-one::before,
.hv-one .img-style.s-one::before {
  border-radius: 50%;
}

.hv-one2:hover .img-style2::before {
  opacity: 1;
  visibility: visible;
}
.hv-one2 .img-style2::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  transition: all 0.4s ease-out 0s;
  opacity: 0;
  visibility: hidden;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1;
  border-radius: 10px;
}

.hv-tool {
  position: relative;
  transition: all 0.3s ease;
}

/*------------ preloader ---------------- */
/* PreLoad
-------------------------------------------------------------- */
.preload {
  overflow: hidden;
}

.preload-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #ffffff;
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 99999999999;
  display: flex;
  justify-content: center;
  align-items: center;
}

@keyframes spin {
  100% {
    transform: rotate(360deg);
  }
}
.boxes {
  --size: 32px;
  --duration: 800ms;
  height: calc(var(--size) * 2);
  width: calc(var(--size) * 3);
  position: relative;
  transform-style: preserve-3d;
  transform-origin: 50% 50%;
  margin-top: calc(var(--size) * 1.5 * -1);
  transform: rotateX(60deg) rotateZ(45deg) rotateY(0deg) translateZ(0px);
}
.boxes .box {
  width: var(--size);
  height: var(--size);
  top: 0;
  left: 0;
  position: absolute;
  transform-style: preserve-3d;
}
.boxes .box:nth-child(1) {
  transform: translate(100%, 0);
  animation: box1 var(--duration) linear infinite;
}
.boxes .box:nth-child(2) {
  transform: translate(0, 100%);
  animation: box2 var(--duration) linear infinite;
}
.boxes .box:nth-child(3) {
  transform: translate(100%, 100%);
  animation: box3 var(--duration) linear infinite;
}
.boxes .box:nth-child(4) {
  transform: translate(200%, 0);
  animation: box4 var(--duration) linear infinite;
}
.boxes .box > div {
  --background: var(--primary-color);
  --top: auto;
  --right: auto;
  --bottom: auto;
  --left: auto;
  --translateZ: calc(var(--size) / 2);
  --rotateY: 0deg;
  --rotateX: 0deg;
  position: absolute;
  width: 100%;
  height: 100%;
  background: var(--background);
  top: var(--top);
  right: var(--right);
  bottom: var(--bottom);
  left: var(--left);
  transform: rotateY(var(--rotateY)) rotateX(var(--rotateX)) translateZ(var(--translateZ));
}
.boxes .box > div:nth-child(1) {
  --top: 0;
  --left: 0;
}
.boxes .box > div:nth-child(2) {
  --background: var(--primary-color);
  --right: 0;
  --rotateY: 90deg;
}
.boxes .box > div:nth-child(3) {
  --background: var(--primary-color);
  --rotateX: -90deg;
}
.boxes .box > div:nth-child(4) {
  --background: #e4e4e4;
  --top: 0;
  --left: 0;
  --translateZ: calc(var(--size) * 3 * -1);
}

@keyframes box1 {
  0%, 50% {
    transform: translate(100%, 0);
  }
  100% {
    transform: translate(200%, 0);
  }
}
@keyframes box2 {
  0% {
    transform: translate(0, 100%);
  }
  50% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(100%, 0);
  }
}
@keyframes box3 {
  0%, 50% {
    transform: translate(100%, 100%);
  }
  100% {
    transform: translate(0, 100%);
  }
}
@keyframes box4 {
  0% {
    transform: translate(200%, 0);
  }
  50% {
    transform: translate(200%, 100%);
  }
  100% {
    transform: translate(100%, 100%);
  }
}
/*-------------- section ----------------- */
/*------------ section ---------------- */
.flat-section {
  padding: 100px 0px 100px;
}

.flat-section-v2 {
  padding-top: 80px;
}

.flat-section-v3 {
  padding: 80px 0px;
}

.flat-section-v4 {
  padding: 60px 0px;
}

.flat-section-v5 {
  padding: 120px 0px 80px;
}

.flat-section-v6 {
  padding: 80px 0px 100px;
}
@media (max-width: 767px) {
  .flat-section-v6 {
    padding: 40px 0px 60px;
  }
}

.flat-title-page {
  padding: 10px 0;
  background-color: #f7f7f7;
}
.flat-title-page h2 {
  text-align: left;
  color: #161e2d;
  letter-spacing: 2px;
}
.flat-title-page .breadcrumb {
  margin-top: 0;
  margin-bottom: 0;
  text-align: left;
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 4px;
}
.flat-title-page .breadcrumb li:last-child {
  color: #a3abb0;
}
.flat-title-page.style-2 .breadcrumb {
  margin-top: 0px;
  margin-bottom: 0px;
}

/*------------ blog ---------------- */
.widget-box {
  padding: 24px;
  border-radius: 16px;
}

.flat-blog-item {
  margin-bottom: 40px;
  display: block;
}
.flat-blog-item .img-style {
  position: relative;
}
.flat-blog-item .date-post {
  z-index: 1;
  position: absolute;
  inset-inline-start: 0;
  bottom: 0;
  display: inline-block;
  font-family: var(--heading-font);
  padding: 6px 12px;
  background-color: var(--primary-color);
  color: #ffffff;
  font-size: 12px;
  line-height: 19px;
  letter-spacing: 0.8px;
  font-weight: 600;
}
.flat-blog-item .content-box {
  margin-top: 20px;
}
.flat-blog-item .content-box .title {
  margin-top: 8px;
}
.flat-blog-item .content-box .description {
  margin-top: 12px;
  color: #5c6368;
  font-size: 16px;
  line-height: 26px;
}
.flat-blog-item.style-1 {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 0;
}
.flat-blog-item.style-1 .img-style {
  border-radius: 0;
}
.flat-blog-item.style-1 .content-box {
  position: absolute;
  z-index: 12;
  bottom: 20px;
  left: 20px;
  right: 20px;
}
.flat-blog-item.style-1 .content-box .title a {
  color: #ffffff;
}
.flat-blog-item.style-1 .content-box .date-post {
  position: unset;
}
.flat-blog-item.style-1 .content-box .post-author {
  margin-top: 4px;
}
.flat-blog-item.style-1 .content-box .post-author span, .flat-blog-item.style-1 .content-box .post-author a {
  color: #ffffff;
  font-size: 14px;
  line-height: 24px;
}
.flat-blog-item.style-1::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 219px;
  transition: all 0.4s ease-out 0s;
  background: linear-gradient(180deg, rgba(11, 33, 50, 0) 0%, #161e2d 100%);
}
.flat-blog-item.style-1:hover::after {
  height: 100%;
}

.post-author span {
  font-family: var(--heading-font);
}
.post-author span:not(:first-child) {
  color: #5c6368;
  margin-inline-start: 4px;
  padding-inline-start: 8px;
  position: relative;
}
.post-author span:not(:first-child) a {
  color: #5c6368;
}
.post-author span:not(:first-child)::before {
  position: absolute;
  content: "";
  width: 1px;
  background-color: #e4e4e4;
  inset-inline-start: 0;
  top: 5px;
  bottom: 5px;
}
.post-author.style-1 span {
  color: #161e2d;
}

.flat-blog-list {
  padding-right: 6%;
}
.flat-blog-list .flat-blog-item {
  margin-bottom: 40px;
  padding-bottom: 40px;
  border-bottom: 1px solid #e4e4e4;
}
.flat-blog-list .flat-blog-item .content-box {
  margin-top: 32px;
}
.flat-blog-list .flat-blog-item .content-box .title {
  margin-top: 12px;
}
.flat-blog-list .flat-blog-item .content-box .description {
  margin-top: 16px;
}
.flat-blog-list .flat-blog-item .content-box .btn-read-more {
  margin-top: 16px;
}
.flat-blog-list .flat-blog-item .img-style {
  border-radius: 20px;
  overflow: hidden;
}
.flat-blog-list .flat-blog-item .date-post {
  padding: 8px 16px;
  font-size: 14px;
  line-height: 20.23px;
}
.flat-blog-list .flat-blog-item:last-child {
  padding-bottom: 0;
  border-bottom: 0;
}

.sidebar-blog .search-box {
  margin-top: 20px;
}
.sidebar-blog .widget-box {
  margin-top: 30px;
}
.sidebar-blog .recent ul {
  margin-top: 20px;
}
.sidebar-blog .recent ul li:last-child .recent-post-item {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: 0;
}
.sidebar-blog .recent .recent-post-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding-bottom: 24px;
  margin-bottom: 24px;
  border-bottom: 1px solid #e4e4e4;
}
.sidebar-blog .recent .recent-post-item .img-style {
  border-radius: 8px;
  width: 110px;
  height: 110px;
  flex-shrink: 0;
}
.sidebar-blog .recent .recent-post-item .content .subtitle {
  font-size: 12px;
  line-height: 19px;
  letter-spacing: 0.8px;
  color: #5c6368;
  font-family: var(--heading-font);
  font-weight: 600;
}
.sidebar-blog .recent .recent-post-item .content .title {
  font-size: 18px;
  line-height: 28px;
  font-weight: 700;
  color: #161e2d;
}
.sidebar-blog .categories ul {
  margin-top: 20px;
}
.sidebar-blog .categories ul li:last-child .categories-item {
  margin-bottom: 0;
}
.sidebar-blog .categories .categories-item {
  margin-bottom: 16px;
  display: flex;
  gap: 4px;
  color: #5c6368;
}
.sidebar-blog .categories .categories-item span {
  transition: all 0.3s ease;
}
.sidebar-blog .categories .categories-item span:first-child {
  position: relative;
}
.sidebar-blog .categories .categories-item span:first-child::before {
  content: "";
  width: 0;
  height: 1px;
  bottom: 0px;
  position: absolute;
  left: auto;
  right: 0;
  z-index: 1;
  transition: width 0.6s cubic-bezier(0.25, 0.8, 0.25, 1) 0s;
  background: var(--primary-color);
}
.sidebar-blog .categories .categories-item span:last-child {
  color: #a3abb0;
}
.sidebar-blog .categories .categories-item:hover span, .sidebar-blog .categories .categories-item.current span {
  color: #161e2d;
}
.sidebar-blog .categories .categories-item:hover span:first-child::before, .sidebar-blog .categories .categories-item.current span:first-child::before {
  width: 100%;
  left: 0;
  right: auto;
}
.sidebar-blog .tag ul {
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
  -moz-column-gap: 16px;
       column-gap: 16px;
  row-gap: 12px;
}
.sidebar-blog .tag .tag-item {
  position: relative;
  font-size: 12px;
  line-height: 19px;
  font-family: var(--heading-font);
  font-weight: 600;
  color: #5c6368;
}
.sidebar-blog .tag .tag-item::before {
  content: "";
  width: 0;
  height: 1px;
  bottom: 0px;
  position: absolute;
  left: auto;
  right: 0;
  z-index: 1;
  transition: width 0.6s cubic-bezier(0.25, 0.8, 0.25, 1) 0s;
  background: var(--primary-color);
}
.sidebar-blog .tag .tag-item:hover, .sidebar-blog .tag .tag-item.current {
  color: #161e2d;
}
.sidebar-blog .tag .tag-item:hover::before, .sidebar-blog .tag .tag-item.current::before {
  width: 100%;
  left: 0;
  right: auto;
}

.flat-banner-blog img {
  width: 100%;
}

.flat-blog-detail h3 {
  margin-top: 12px;
  margin-bottom: 16px;
}
.flat-blog-detail .post-navigation {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  padding: 16px 0px;
  border-top: 1px solid #e4e4e4;
  border-bottom: 1px solid #e4e4e4;
}
.flat-blog-detail .post-navigation .previous-post {
  padding-inline-end: 22%;
  position: relative;
}
.flat-blog-detail .post-navigation .previous-post::after {
  position: absolute;
  content: "";
  width: 1px;
  background-color: #e4e4e4;
  inset-inline-end: 0;
  top: 13px;
  bottom: 13px;
}
.flat-blog-detail .post-navigation .next-post {
  text-align: end;
  padding-inline-start: 30%;
}
.flat-blog-detail .post-navigation .subtitle {
  font-weight: 700;
  color: #a3abb0;
  margin-bottom: 4px;
  font-size: 16px;
  line-height: 26px;
  letter-spacing: 0.8px;
}
.flat-blog-detail .wrap-review {
  margin-top: 80px;
}
.flat-blog-detail .wrap-review .box-review {
  margin-top: 20px;
}
.flat-blog-detail .wrap-form-comment {
  margin-top: 40px;
}

.flat-quote {
  padding: 32px 40px;
  border-radius: 12px;
  background-color: #f7f7f7;
  border-left: 4px solid var(--primary-color);
}
.flat-quote .quote {
  font-size: 24px;
  line-height: 30px;
  font-weight: 700;
  color: #161e2d;
  margin-bottom: 16px;
}
.flat-quote .author {
  color: #5c6368;
  font-size: 16px;
  line-height: 26px;
  letter-spacing: 0.8px;
  font-weight: 700;
  position: relative;
  padding-left: 28px;
}
.flat-quote .author::before {
  position: absolute;
  content: "";
  height: 1px;
  width: 20px;
  left: 10px;
  top: 50%;
  transform: translateX(-50%);
  background-color: #5c6368;
}

.blog-tag {
  padding: 8px 16px;
  background-color: #f7f7f7;
  font-size: 12px;
  line-height: 19px;
  letter-spacing: 0.8px;
  font-family: var(--heading-font);
  font-weight: 600;
}
.blog-tag:hover {
  background-color: var(--primary-color);
  color: #ffffff;
}
.blog-tag.primary {
  background-color: var(--primary-color);
  color: #ffffff;
  padding: 6px 10px;
}

.flat-latest-post .box-title-relatest {
  margin-bottom: 30px;
}
.flat-latest-post .flat-blog-item {
  margin-bottom: 0;
}

/*------------ pagination ---------------- */
.flat-pagination {
  display: inline-flex;
  align-items: center;
  gap: 6px;
}
.flat-pagination .page-numbers {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 700;
  color: #161e2d;
  text-align: center;
  width: 48px;
  height: 50px;
  line-height: 50px;
  border-radius: 8px;
  -o-object-fit: cover;
     object-fit: cover;
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;
  border: 1px solid #ffffff;
}
.flat-pagination .page-numbers.current, .flat-pagination .page-numbers:hover {
  background-color: #ffffff;
  color: #000000;
  border: 1px solid #9a9a9a;
}
.flat-pagination .page-numbers svg {
  width: 2rem;
  height: 2rem;
}

.list-star {
  display: flex;
  list-style: none !important;
}
.list-star .icon-star {
  color: #f4d118;
  font-size: 16px;
}

.list-review-item {
  display: flex;
  gap: 20px;
}
.list-review-item .avatar {
  flex-shrink: 0;
}
.list-review-item:not(:last-child) .content {
  padding-bottom: 28px;
  margin-bottom: 28px;
  border-bottom: 1px solid #e4e4e4;
}
.list-review-item .box-img-review {
  margin-top: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}
.list-review-item .box-img-review .img-review {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f7f7f7;
}
.list-review-item .view-question {
  margin-top: 22px;
  font-weight: 700;
  display: inline-block;
  border-bottom: 1px solid #161e2d;
}

.flag-tag {
  font-weight: 600;
  font-size: 12px;
  line-height: 20px;
  letter-spacing: 0.8px;
  text-align: center;
  display: inline-block;
  padding: 0px 8px;
  border-radius: 4px;
  background-color: rgba(11, 33, 50, 0.4);
  color: #ffffff;
  transition: all 0.3s ease;
}
.flag-tag.style-1 {
  background-color: rgba(11, 33, 50, 0.4);
}
.flag-tag.style-2 {
  background-color: #ffffff;
  color: #161e2d;
  font-size: 14px;
  line-height: 24px;
  letter-spacing: 0.8px;
}
.flag-tag.style-3 {
  font-size: 16px;
  line-height: 26px;
}
.flag-tag.primary {
  background-color: var(--primary-color);
}
.flag-tag:hover {
  color: #ffffff;
  background-color: var(--primary-color);
}
.flag-tag.success {
  color: #ffffff;
  background-color: #198754;
}

.info-box {
  background: #ffffff;
  border-radius: 12px;
}
.info-box .box-top {
  padding: 20px 20px 16px;
  border-bottom: 1px solid #e4e4e4;
}
.info-box .title {
  margin-top: 12px;
}
.info-box .desc {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 8px;
  color: #5c6368;
}
.info-box .meta-list {
  margin-top: 20px;
  display: flex;
  align-items: center;
  -moz-column-gap: 30px;
       column-gap: 30px;
  row-gap: 15px;
  flex-wrap: wrap;
}
.info-box .meta-list .item {
  display: flex;
  align-items: center;
  gap: 12px;
}
.info-box .meta-list .item .icon {
  font-size: 28px;
  color: #5c6368;
}
.info-box .meta-list .item span {
  font-weight: 700;
  font-size: 18px;
  line-height: 28px;
}
.info-box .box-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.flat-filter-search.home-5 {
  position: relative;
  z-index: 5;
}

.flat-filter-search-v2 .flat-tab-form {
  display: flex;
  padding: 18px 30px;
  gap: 30px;
  border: 1px solid #e4e4e4;
}
.flat-filter-search-v2 .flat-tab-form .nav-tab-form {
  gap: 16px;
}
.flat-filter-search-v2 .flat-tab-form .nav-tab-form .nav-link-item {
  border-radius: 8px;
  background-color: #f7f7f7;
  color: #161e2d;
}
.flat-filter-search-v2 .flat-tab-form .tab-content {
  position: relative;
  flex-grow: 1;
}
.flat-filter-search-v2 .flat-tab-form .wd-find-select {
  gap: 30px;
}
.flat-filter-search-v2 .flat-tab-form .wd-find-select .inner-group {
  padding: 0px;
}
.flat-filter-search-v2 .flat-tab-form .wd-find-select .tf-btn {
  padding: 11px 20px;
  border-radius: 4px;
}
.flat-filter-search-v2 .flat-tab-form .wd-search-form {
  top: 131%;
  margin-top: 0;
}

.homeya-box {
  display: block;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e4e4e4;
}
.homeya-box .images-group {
  position: relative;
  display: block;
  flex-shrink: 0;
}
.homeya-box .images-group .top {
  left: 16px;
  right: 16px;
  top: 16px;
  display: flex;
  justify-content: space-between;
  position: absolute;
  align-items: flex-start;
  z-index: 1;
  gap: 8px;
  flex-wrap: wrap;
}
.homeya-box .images-group .box-icon {
  background-color: rgba(11, 33, 50, 0.4);
  border-radius: 4px;
  border: none;
}
.homeya-box .images-group .box-icon.w-40 .icon {
  color: #ffffff;
  font-size: 24px;
}
.homeya-box .images-group .box-icon.w-32 .icon {
  color: #ffffff;
  height: 20px;
  width: 20px;
}
.homeya-box .images-group .box-icon.w-28 .icon {
  color: #ffffff;
  font-size: 16px;
}
.homeya-box .images-group .box-icon:hover {
  background-color: var(--primary-color);
}
.homeya-box .images-group .bottom {
  position: absolute;
  inset-inline-start: 16px;
  bottom: 16px;
  z-index: 1;
}
.homeya-box .images-group:after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5019607843);
  opacity: 0;
  transition: all 0.3s ease;
}
.homeya-box .images-style {
  position: relative;
  overflow: hidden;
}
.homeya-box .images-style img {
  transition: all 0.3s ease;
  width: 100%;
  aspect-ratio: 16/9;
  -o-object-fit: cover;
     object-fit: cover;
}
.homeya-box .content {
  padding: 16px 20px;
  border-bottom: 1px solid #e4e4e4;
}
.homeya-box .content .desc {
  display: flex;
  gap: 4px;
  margin-top: 8px;
  color: #5c6368;
}
.homeya-box .content .desc .icon {
  margin-top: 4px;
}
.homeya-box .content .desc p {
  font-size: 16px;
  line-height: 26px;
}
.homeya-box .content .meta-list {
  margin-top: 12px;
  display: flex;
  align-items: center;
  -moz-column-gap: 30px;
       column-gap: 30px;
  row-gap: 15px;
  flex-wrap: wrap;
}
.homeya-box .content .meta-list .item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: var(--heading-font);
}
.homeya-box .content .meta-list .item .icon {
  font-size: 24px;
  color: #5c6368;
}
.homeya-box .content .meta-list .item span {
  font-weight: 600;
}
.homeya-box .content .archive-bottom {
  padding: 0;
}
.homeya-box .archive-bottom {
  padding: 16px 20px;
  flex-wrap: wrap;
  gap: 5px;
}
.homeya-box:hover .images-group::after {
  opacity: 1;
}
.homeya-box:hover .images-style img {
  transform: scale(1.05);
}
.homeya-box.lg .images-group .top {
  flex-wrap: wrap;
}
.homeya-box.lg .content {
  padding: 24px 30px;
}
.homeya-box.lg .content .desc {
  margin-top: 12px;
}
.homeya-box.lg .content .desc .icon {
  font-size: 20px;
}
.homeya-box.lg .content .desc p {
  font-size: 18px;
  line-height: 28px;
}
.homeya-box.lg .content .note {
  font-size: 20px;
  line-height: 30px;
  margin-top: 16px;
}
.homeya-box.lg .content .meta-list {
  margin-top: 16px;
  -moz-column-gap: 40px;
       column-gap: 40px;
  row-gap: 15px;
  flex-wrap: wrap;
}
.homeya-box.lg .content .meta-list .item .icon {
  font-size: 28px;
}
.homeya-box.lg .content .meta-list .item span {
  font-weight: 700;
  font-size: 20px;
  line-height: 28px;
}
.homeya-box.lg .archive-bottom {
  padding: 24px 30px;
}
.homeya-box.md .content {
  padding: 16px;
}
.homeya-box.md .content .desc p {
  font-size: 14px;
  line-height: 22px;
}
.homeya-box.md .content .meta-list {
  margin-top: 16px;
  -moz-column-gap: 20px;
       column-gap: 20px;
}
.homeya-box.md .content .meta-list .item .icon {
  font-size: 20px;
}
.homeya-box.md .content .meta-list .item span {
  font-weight: 500;
  font-size: 16px;
  line-height: 26px;
  display: inline-block;
}
.homeya-box.md .content .archive-bottom {
  padding: 16px;
}
.homeya-box.style-2 .images-group:after {
  content: "";
  position: absolute;
  top: unset;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 132px;
  transition: all 0.4s ease;
  background: linear-gradient(180deg, rgba(11, 33, 50, 0) 0%, rgba(11, 33, 50, 0.9) 100%);
  opacity: 1;
}
.homeya-box.style-2 .images-group .flag-tag.style-2 {
  background-color: transparent;
  color: #ffffff;
  padding: 0;
}
.homeya-box.style-2:hover .images-group:after {
  height: 100%;
}
.homeya-box.style-3 {
  border: 0;
}
.homeya-box.style-3 .images-group {
  position: relative;
}
.homeya-box.style-3 .images-group .images-style {
  height: 308px;
}
.homeya-box.style-3 .images-group .images-style img {
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.homeya-box.style-3 .images-group .content {
  position: absolute;
  left: 16px;
  right: 16px;
  bottom: 16px;
  z-index: 50;
  padding: 0;
  border: 0;
}
.homeya-box.style-3 .images-group .content .pricing {
  margin-top: 4px;
  margin-bottom: 8px;
}
.homeya-box.style-3 .images-group .content .meta-list {
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.homeya-box.style-3 .images-group .content .meta-list .icon,
.homeya-box.style-3 .images-group .content .meta-list span {
  color: #ffffff;
}
.homeya-box.style-3 .images-group:after {
  content: "";
  position: absolute;
  top: unset;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 160px;
  transition: all 0.4s ease;
  background: linear-gradient(180deg, rgba(11, 33, 50, 0) 0%, #161e2d 100%);
  opacity: 1;
}
.homeya-box.style-3:hover .images-group:after {
  height: 100%;
}
.homeya-box.list-style-1 {
  display: flex;
}
.homeya-box.list-style-1 .images-style {
  height: 100%;
}
.homeya-box.list-style-1 .images-style img {
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.homeya-box.list-style-1 .content {
  flex-grow: 1;
  padding: 19px 20px;
  background-color: #ffffff;
  border: 0;
}
.homeya-box.list-style-1 .content .meta-list {
  margin-top: 14px;
}
.homeya-box.list-style-1 .content .archive-top {
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e4e4e4;
}
.homeya-box.list-style-2 .images-style {
  width: 330px;
}
.homeya-box.list-style-2 .images-style img {
  -o-object-fit: unset;
     object-fit: unset;
}

.box-title {
  margin-bottom: 40px;
}
.box-title.style-1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 15px;
}
.box-title.style-2 {
  max-width: 640px;
  margin-left: auto;
  margin-right: auto;
}

.flat-recommended .homeya-box {
  margin-bottom: 30px;
}
.flat-recommended .tf-btn {
  margin-top: 10px;
}

.flat-recommended-v2 {
  margin-top: -55px;
}

.box-location {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  display: block;
  transition: all 0.6s ease;
}
.box-location .content {
  position: absolute;
  bottom: 18px;
  left: 16px;
  right: 16px;
  z-index: 12;
  padding: 17px 24px;
  border-radius: 12px;
  transition: all 0.6s ease;
}
.box-location .content .sub-title {
  font-size: 16px;
  line-height: 26px;
  color: #ffffff;
  transition: all 0.6s ease;
}
.box-location .content .title {
  color: #ffffff;
  transition: all 0.6s ease;
}
.box-location .image {
  width: 100%;
}
.box-location .image img {
  border-radius: 16px;
  width: 100%;
}
.box-location::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 270px;
  transition: all 0.4s ease-out 0s;
  background: linear-gradient(180deg, rgba(11, 33, 50, 0) 0%, rgba(11, 33, 50, 0.9) 100%);
}
.box-location.style-1 .content {
  bottom: 12px;
  left: 12px;
  right: 12px;
  padding: 12px 16px;
}
.box-location.style-1 .content .sub-title {
  font-size: 14px;
  line-height: 22px;
}
.box-location.style-1 .content .title {
  font-size: 18px;
  line-height: 28px;
}
.box-location.style-1::after {
  height: 220px;
}
.box-location.active .content {
  background-color: #ffffff;
}
.box-location.active .content .sub-title {
  color: #a3abb0;
}
.box-location.active .content .title {
  color: #161e2d;
}
.box-location:hover .content {
  background-color: #ffffff;
}
.box-location:hover .content .sub-title {
  color: #a3abb0;
}
.box-location:hover .content .title {
  color: #161e2d;
}
.box-location:hover::after {
  opacity: 0;
}

.overlay .swiper-slide {
  position: relative;
}
.overlay .swiper-slide:not(.swiper-slide-prev, .swiper-slide-active, .swiper-slide-next)::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: #fff;
  opacity: 0.4;
  transition: all 0.4s ease;
  z-index: 100;
}

.flat-location .navigation {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 123;
}
.flat-location .navigation.swiper-nav-next {
  left: 60px;
}
.flat-location .navigation.swiper-nav-prev {
  right: 60px;
}

.flat-location-v2 {
  position: relative;
}
.flat-location-v2 .navigation {
  top: 60%;
  transform: translateY(-50%);
}

.grid-location {
  display: grid;
  grid-template-areas: "item1 item2 item3 item4" "item5 item5 item6 item6";
  grid-column-gap: 30px;
  grid-row-gap: 28px;
}
.grid-location .item-1 {
  grid-area: item1;
}
.grid-location .item-2 {
  grid-area: item2;
}
.grid-location .item-3 {
  grid-area: item3;
}
.grid-location .item-4 {
  grid-area: item4;
}
.grid-location .item-5 {
  grid-area: item5;
}
.grid-location .item-6 {
  grid-area: item6;
}

.box-location-v2 .box-img {
  border-radius: 16px;
  max-height: 12rem;
}
@media (min-width: 768px) {
  .box-location-v2 .box-img {
    max-height: 24rem;
  }
}
.box-location-v2:nth-child(5) .box-img, .box-location-v2:nth-child(6) .box-img {
  max-height: 24rem;
}
@media (min-width: 768px) {
  .box-location-v2:nth-child(5) .box-img, .box-location-v2:nth-child(6) .box-img {
    max-height: 54rem;
  }
}
.box-location-v2 .content {
  padding-top: 20px;
}
.box-location-v2 .content p {
  color: #5c6368;
  font-size: 16px;
  line-height: 26px;
  margin-top: 8px;
}

.grid-location-v2 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.box-location-v3 {
  display: flex;
  align-items: center;
  gap: 24px;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f7f7f7;
  box-shadow: 0px 2px 2px 0px rgba(28, 36, 51, 0.**********);
  transition: all 0.3s ease;
}
.box-location-v3 .img-style {
  border-radius: 0;
}
.box-location-v3 .content p {
  margin-top: 4px;
  font-size: 16px;
  line-height: 26px;
  transition: all 0.3s ease;
}
.box-location-v3 .content .btn-view {
  margin-top: 4px;
}
.box-location-v3 .content .btn-view .text {
  font-size: 14px;
  line-height: 24px;
  font-weight: 500;
}
.box-location-v3 .content .btn-view .text::before {
  background-color: #ffffff;
}
.box-location-v3.active, .box-location-v3:hover {
  background-color: var(--primary-color);
}
.box-location-v3.active .content h6 a,
.box-location-v3.active .content p, .box-location-v3:hover .content h6 a,
.box-location-v3:hover .content p {
  color: #ffffff;
}
.box-location-v3.active .content .btn-view .text,
.box-location-v3.active .content .btn-view .icon, .box-location-v3:hover .content .btn-view .text,
.box-location-v3:hover .content .btn-view .icon {
  color: #ffffff;
}

.box-service {
  display: flex;
  flex-direction: column;
  gap: 30px;
  transition: all 0.5s ease;
}
.box-service .icon-box .icon {
  width: 80px;
  height: 80px;
  transition: all 0.8s ease;
  display: inline-block;
}
.box-service .content .description {
  margin-top: 12px;
  font-size: 16px;
  line-height: 26px;
  color: #5c6368;
}
.box-service .content .btn-view {
  margin-top: 12px;
}
.box-service.style-1 {
  flex-direction: row;
  align-items: center;
  padding: 28px;
  background-color: #ffffff;
  box-shadow: 0 10px 25px 0 rgba(54, 95, 104, 0.**********);
  border-radius: 16px;
}
.box-service.style-1 .content .btn-view {
  margin-top: 10px;
}
.box-service:hover .icon-box .icon {
  transform: rotateY(360deg);
}
.box-service.style-2 {
  align-items: center;
  padding: 40px 30px;
  border-radius: 16px;
}
.box-service.style-2 .content {
  text-align: center;
}
.box-service.style-2 .content .tf-btn {
  margin-top: 20px;
}
.box-service.style-2.active, .box-service.style-2:hover {
  background-color: #f7f7f7;
}
.box-service.style-2.active .tf-btn, .box-service.style-2:hover .tf-btn {
  background-color: var(--primary-color);
  color: #ffffff;
  border-color: var(--primary-color);
}
.box-service.style-3 {
  padding: 40px 30px;
  align-items: center;
  border-radius: 20px;
}
.box-service.style-3 .content {
  text-align: center;
}
.box-service.style-3.active, .box-service.style-3:hover {
  background-color: #ffffff;
}
.box-service.style-3.active .btn-view .text, .box-service.style-3:hover .btn-view .text {
  color: #161e2d;
}
.box-service.style-3.active .btn-view .text::before, .box-service.style-3:hover .btn-view .text::before {
  width: 100%;
  left: 0;
  right: auto;
}
.box-service.style-4 {
  flex-direction: row;
}
.box-service.style-4 .icon-box {
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 1000px;
  background-color: rgba(224, 80, 40, 0.1);
  flex-shrink: 0;
}
.box-service.style-4 .icon-box .icon {
  color: var(--primary-color);
  height: 52px;
  width: 52px;
}
.box-service.style-4 .content .btn-view {
  margin-top: 8px;
}

.wrap-service {
  display: flex;
  gap: 60px;
}

.flat-service {
  padding-bottom: 60px;
  margin-bottom: 60px;
  border-bottom: 1px solid #e4e4e4;
}

.wrap-service-v2 {
  align-items: center;
}
.wrap-service-v2 .box-left {
  padding-right: 100px;
}
.wrap-service-v2 .box-left p {
  font-size: 16px;
  line-height: 26px;
  color: #5c6368;
}
.wrap-service-v2 .box-left .list-view {
  margin-top: 28px;
  margin-bottom: 28px;
  padding-left: 0 !important;
}
.wrap-service-v2 .list-view {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-row-gap: 12px;
}
.wrap-service-v2 .list-view li {
  font-family: var(--heading-font);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  line-height: 28px;
  font-weight: 700;
}
.wrap-service-v2 .list-view li:last-child, .wrap-service-v2 .list-view li:nth-child(2) {
  padding-left: 45px;
}
.wrap-service-v2 .box-title {
  margin-bottom: 28px;
}
.wrap-service-v2 .box-right {
  padding-left: 80px;
  padding-right: 40px;
}
.wrap-service-v2 .box-right .box-service:not(:last-child) {
  margin-bottom: 20px;
}
.wrap-service-v2 .box-right .box-service {
  cursor: pointer;
  transition: all 0.5s ease;
}
.wrap-service-v2 .box-right .box-service:hover, .wrap-service-v2 .box-right .box-service.active {
  transform: scale(1.08);
}
.wrap-service-v2 .box-right .box-service.active {
  margin: 30px 0px;
}

.wrap-service-v4 {
  display: flex;
  gap: 36px;
}
.wrap-service-v4 .inner-service-left {
  padding-top: 8px;
  padding-right: 94px;
  flex-shrink: 0;
}
.wrap-service-v4 .inner-service-left .img-service {
  position: relative;
}
.wrap-service-v4 .inner-service-left .img-service img {
  border-radius: 1000px 1000px 0px 0px;
}
.wrap-service-v4 .box-avatar {
  position: absolute;
  left: -111px;
  top: 25%;
  transform: rotate(-16deg);
  display: inline-flex;
  padding: 16px 24px 16px 16px;
  border-radius: 73px;
  gap: 16px;
  background-color: #ffffff;
  box-shadow: 0px 10px 25px 0px rgba(54, 95, 104, 0.**********);
  animation: ani1 7s infinite ease-in-out alternate;
}
.wrap-service-v4 .box-avatar .avatar {
  position: relative;
}
.wrap-service-v4 .box-avatar .avatar img {
  border-radius: 50%;
}
.wrap-service-v4 .box-avatar .avatar .status {
  position: absolute;
  width: 16px;
  height: 16px;
  background-color: var(--primary-color);
  border: 2px solid #ffffff;
  border-radius: 50%;
  bottom: 0;
  right: -2px;
}
.wrap-service-v4 .box-trader {
  position: absolute;
  right: -50px;
  bottom: 13%;
  transform: rotate(16deg);
  background-color: transparent;
  padding: 8px;
  border: 4px solid var(--primary-color);
  border-radius: 20px;
  display: inline-flex;
  animation: ani2 7s infinite ease-in-out alternate;
}
.wrap-service-v4 .box-trader .content {
  background-color: #ffffff;
  border-radius: 20px;
  padding: 8px 36px;
  text-align: center;
  box-shadow: 0px 10px 25px 0px rgba(54, 95, 104, 0.**********);
}
.wrap-service-v4 .inner-service-right {
  padding-right: 89px;
}
.wrap-service-v4 .inner-service-right .box-title {
  margin-bottom: 50px;
}
.wrap-service-v4 .inner-service-right .box-title p {
  margin-top: 12px;
  font-size: 16px;
  line-height: 26px;
  color: #5c6368;
}
.wrap-service-v4 .inner-service-right .box-service:not(:last-child) {
  margin-bottom: 40px;
}

@keyframes ani1 {
  0%, 100% {
    transform: translateX(0);
    transition: all 0.3s ease;
  }
  50% {
    transform: rotate(-16deg);
    transition: all 0.3s ease;
  }
}
@keyframes ani2 {
  0%, 100% {
    transform: translateX(0);
    transition: all 0.3s ease;
  }
  50% {
    transform: rotate(16deg);
    transition: all 0.3s ease;
  }
}
.flat-service-v5 {
  margin-top: -60px;
  gap: 36px;
}

.wrap-counter {
  display: flex;
  justify-content: space-between;
}
.wrap-counter .counter-box {
  display: flex;
  align-items: center;
  gap: 12px;
}
.wrap-counter .counter-box .number {
  font-size: 64px;
  line-height: 66px;
  font-family: var(--heading-font);
  color: var(--primary-color);
  font-weight: 600;
}
.wrap-counter .counter-box .title-count {
  font-family: var(--heading-font);
  font-size: 24px;
  line-height: 30px;
  font-weight: 700;
  letter-spacing: 0.8px;
  color: #161e2d;
}

.box-benefit {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}
.box-benefit .icon-box .icon {
  font-size: 80px;
  transition: all 0.8s ease;
  display: inline-block;
}
.box-benefit .content .description {
  margin-top: 12px;
  font-size: 16px;
  line-height: 26px;
  color: #5c6368;
}
.box-benefit:hover .icon-box .icon {
  transform: rotateY(180deg);
}
.box-benefit.style-1 {
  align-items: flex-start;
}
.box-benefit.style-1 .icon-box .icon {
  width: 60px;
  height: 60px;
  color: #ffffff;
}
.box-benefit.style-1 .content .link {
  color: #ffffff;
}
.box-benefit.style-1 .content .description {
  margin-top: 9px;
  color: #ffffff;
}

.wrap-benefit {
  display: flex;
  gap: 60px;
}

.flat-benefit-v2 {
  background: #161e2d;
  padding: 80px 0px;
}

.wrap-benefit-v2 .box-left .box-title {
  margin-bottom: 20px;
}
.wrap-benefit-v2 .box-left .description {
  font-size: 16px;
  line-height: 26px;
}
.wrap-benefit-v2 .box-right {
  display: grid;
  grid-template-columns: 1fr 1fr;
  -moz-column-gap: 36px;
       column-gap: 36px;
  row-gap: 30px;
  padding-left: 70px;
}
.wrap-benefit-v2 .box-navigation {
  margin-top: 30px;
}

.wrap-property .box-right {
  flex-grow: 1;
}
.wrap-property .box-right .homeya-box:not(:last-child) {
  margin-bottom: 30px;
}

.wrap-property-v2 {
  display: flex;
}
.wrap-property-v2 .box-inner-left {
  width: 50%;
}
.wrap-property-v2 .box-inner-left img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.wrap-property-v2 .box-inner-right {
  width: 50%;
  padding: 80px;
  background-color: #f7f7f7;
}
.wrap-property-v2 .box-inner-right .content-property .box-tag {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}
.wrap-property-v2 .box-inner-right .content-property .box-tag .flag-tag {
  font-size: 16px;
  line-height: 26px;
  font-weight: 700;
}
.wrap-property-v2 .box-inner-right .content-property .box-name .location {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 18px;
  line-height: 28px;
  color: #5c6368;
}
.wrap-property-v2 .box-inner-right .content-property .box-name .location .icon {
  font-size: 20px;
}
.wrap-property-v2 .box-inner-right .content-property .list-info {
  display: flex;
  align-items: center;
  gap: 60px;
  flex-wrap: wrap;
  padding-top: 10px;
  margin-top: 16px;
}
.wrap-property-v2 .box-inner-right .content-property .list-info .item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 20px;
  line-height: 28px;
  font-weight: 700;
  color: #161e2d;
}
.wrap-property-v2 .box-inner-right .content-property .list-info .item .icon {
  font-size: 32px;
  color: #5c6368;
}
.wrap-property-v2 .box-inner-right .content-property .box-avatar {
  margin-top: 40px;
}
.wrap-property-v2 .box-inner-right .content-property .pricing-property {
  margin-top: 40px;
  display: flex;
  align-items: center;
  gap: 248px;
}
.wrap-property-v2 .box-inner-right .content-property .pricing-property .box-icon {
  background-color: #ffffff;
  border-radius: 4px;
  border: 1px solid #e4e4e4;
}
.wrap-property-v2 .box-inner-right .content-property .pricing-property .box-icon .icon {
  font-size: 28px;
}
.wrap-property-v2 .box-inner-right .content-property .pricing-property .box-icon:hover {
  background-color: var(--primary-color);
}
.wrap-property-v2 .box-inner-right .content-property .pricing-property .box-icon:hover .icon {
  color: #ffffff;
}
.wrap-property-v2 .box-inner-right .sw-pagination {
  margin-top: 40px;
}
.wrap-property-v2.style-1 {
  border-radius: 20px;
  overflow: hidden;
}
.wrap-property-v2.style-1 .box-inner-right {
  padding: 60px;
}
.wrap-property-v2.style-1 .box-inner-right .content-property .pricing-property {
  gap: 30px;
  justify-content: space-between;
}

.wrap-sw-property {
  position: relative;
}
.wrap-sw-property .tf-sw-property {
  border-radius: 20px;
}

.list-star {
  display: flex;
  align-items: center;
  list-style: none !important;
}
.list-star .icon {
  font-size: 24px;
  color: #f4d118;
}

.box-tes-item {
  padding: 40px;
  padding-left: 32px;
  border-radius: 20px;
  background-color: #ffffff;
}
.box-tes-item .note {
  margin-top: 12px;
}
.box-tes-item .box-avt {
  margin-top: 24px;
}
.box-tes-item.style-1 {
  box-shadow: 0px 5px 15px 0px rgba(54, 95, 104, 0.**********);
}
.box-tes-item.style-2 {
  background-color: #f7f7f7;
}

.box-test-left {
  border-top-left-radius: 16px;
  border-bottom-left-radius: 16px;
  height: 100%;
  overflow: hidden;
}
.box-test-left .img-style {
  position: relative;
  border-radius: 0;
}
.box-test-left .img-style .title {
  position: absolute;
  z-index: 12;
  bottom: 24px;
  left: 40px;
  right: 60px;
}
.box-test-left .img-style::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 127px;
  transition: all 0.4s ease-out 0s;
  background: linear-gradient(180deg, rgba(11, 33, 50, 0) 0%, rgba(11, 33, 50, 0.9) 100%);
  opacity: 0.7;
}
.box-test-left .content-box {
  padding: 30px 40px 40px;
  background-color: #161e2d;
  height: 100%;
}
.box-test-left .content-box .tf-btn {
  margin-top: 20px;
}
.box-test-left:hover .img-style::after {
  height: 100%;
}

.box-test-right {
  border-top-right-radius: 16px;
  border-bottom-right-radius: 16px;
  overflow: hidden;
  background-color: #f7f7f7;
  height: 100%;
  margin-left: -30px;
  padding: 60px 60px 30px 60px;
}
.box-test-right .tf-sw-testimonial {
  padding-bottom: 38px;
}
.box-test-right .tf-sw-testimonial .sw-pagination {
  margin-top: 4px;
}
.box-test-right .box-tes-item-v2 {
  text-align: center;
}
.box-test-right .box-tes-item-v2 .list-star {
  justify-content: center;
  margin-bottom: 24px;
  list-style: none !important;
}
.box-test-right .wrap-partner {
  margin-top: 30px;
}

.flat-testimonial {
  overflow: hidden;
}
.flat-testimonial .box-title {
  margin-bottom: 20px;
}
.flat-testimonial .box-navigation {
  margin-top: 30px;
}
.flat-testimonial .swiper-slide .box-tes-item {
  opacity: 0.4;
}
.flat-testimonial .swiper-slide-prev .box-tes-item,
.flat-testimonial .swiper-slide-active .box-tes-item,
.flat-testimonial .swiper-slide-next .box-tes-item {
  opacity: 1;
}

.tf-sw-testimonial .sw-pagination {
  margin-top: 40px;
  text-align: center;
}

.flat-testimonial-v2 {
  position: relative;
}
.flat-testimonial-v2 .tf-sw-testimonial {
  padding: 15px;
  margin: -15px;
}
.flat-testimonial-v2::before {
  position: absolute;
  content: "";
  left: 0;
  right: 0;
  top: 0;
  height: 400px;
  background: #161e2d;
}

.box-agent {
  display: flex;
  flex-direction: column;
  gap: 21px;
}
.box-agent .box-img {
  position: relative;
}
.box-agent .box-img .agent-social {
  position: absolute;
  z-index: 1;
  bottom: 0px;
  background-color: #ffffff;
  padding: 12px 0px;
  border-radius: 8px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  left: 37px;
  right: 37px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}
.box-agent .box-img .agent-social a {
  display: flex;
  align-items: center;
  justify-content: center;
}
.box-agent .box-img .agent-social .icon {
  color: #a3abb0;
  width: 1.25rem;
  height: 1.25rem;
  stroke-width: 2;
}
.box-agent .box-img .agent-social li:not(:last-child) {
  border-right: 1px solid #e4e4e4;
}
.box-agent .box-img .agent-social li:hover .icon {
  color: var(--primary-color);
}
.box-agent .content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.box-agent .content h6 {
  transition: all 0.3s ease;
}
.box-agent .content p {
  font-size: 16px;
  line-height: 26px;
}
.box-agent .content .icon-phone {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 1000px;
  background-color: #f7f7f7;
  font-size: 28px;
  color: #5c6368;
  transition: all 0.6s ease;
}
.box-agent .content .list-info {
  margin-top: 16px;
}
.box-agent .content .list-info li {
  display: flex;
  gap: 8px;
}
.box-agent .content .list-info li .icon {
  font-size: 20px;
}
.box-agent .content .list-info li:not(:last-child) {
  margin-bottom: 8px;
}
.box-agent .content .tf-btn {
  margin-top: 16px;
}
.box-agent.style-1 .agent-social {
  bottom: 23px;
  top: 23px;
  right: 0;
  left: unset;
  padding: 0px 12px;
  grid-template-columns: repeat(1, 1fr);
  z-index: 1;
  transition: all 0.3s ease;
}
.box-agent.style-1 .agent-social li {
  display: flex;
  align-items: center;
  justify-content: center;
}
.box-agent.style-1 .agent-social li:not(:last-child) {
  border-right: 0;
  border-bottom: 1px solid #e4e4e4;
}
.box-agent:hover .box-img .agent-social {
  bottom: 20px;
  opacity: 1;
  visibility: visible;
}
.box-agent:hover .content .icon-phone {
  background-color: var(--primary-color);
  color: #ffffff;
}
.box-agent:hover.style-1 .box-img .agent-social {
  right: 16px;
}
.box-agent.style-2 {
  flex-direction: row;
  align-items: center;
  gap: 0px;
  background-color: #ffffff;
  border-radius: 20px;
  overflow: hidden;
}
.box-agent.style-2 .box-img {
  border-radius: 0;
  min-width: 20rem;
}
.box-agent.style-2 .content {
  display: block;
  padding: 30px;
}
.box-agent.style-2:hover .tf-btn {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: #ffffff;
}
.box-agent.style-3 {
  gap: 30px;
}
.box-agent.style-3 .content {
  display: block;
}

.flat-agents-v2 .box-title {
  margin-bottom: 30px;
}

.flat-latest-new .flat-blog-item {
  margin-bottom: 0;
}

.tf-sw-partner .partner-item {
  cursor: pointer;
}
.tf-sw-partner .partner-item img {
  transition: all 0.3s ease;
}

.homeya-categories {
  padding: 30px 24px 24px 24px;
  background-color: #f7f7f7;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  transition: all 0.4s ease;
  position: relative;
  z-index: 1;
}
.homeya-categories .icon-box {
  overflow: hidden;
}
.homeya-categories .icon-box .icon {
  transition: all 0.4s ease;
  width: 80px;
  height: 80px;
}
.homeya-categories .content h6,
.homeya-categories .content p {
  transition: all 0.4s ease;
}
.homeya-categories .content p {
  font-size: 16px;
  line-height: 26px;
}
.homeya-categories.active .icon-box .icon, .homeya-categories:hover .icon-box .icon {
  transform: scale(1.05);
  color: #ffffff;
}
.homeya-categories.active .content h6,
.homeya-categories.active .content p, .homeya-categories:hover .content h6,
.homeya-categories:hover .content p {
  color: #ffffff;
}
.homeya-categories.active::before, .homeya-categories:hover::before {
  transform: scale(1, 1);
  transform-origin: top center;
}
.homeya-categories::before {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  content: "";
  background-color: var(--primary-color);
  transform: scale(1, 0);
  transition: transform 400ms ease;
  transform-origin: bottom center;
  z-index: -1;
  border-radius: 8px;
}
.homeya-categories.style-1 {
  background-color: rgba(255, 255, 255, 0.1);
}
.homeya-categories.style-1 .box-icon {
  background-color: #ffffff;
  transition: all 0.4s ease;
}
.homeya-categories.style-1 .box-icon .icon {
  transition: all 0.4s ease;
}
.homeya-categories.style-1 .content h6,
.homeya-categories.style-1 .content p {
  color: #ffffff;
}
.homeya-categories.style-1.active, .homeya-categories.style-1:hover {
  background-color: #ffffff;
}
.homeya-categories.style-1.active .box-icon, .homeya-categories.style-1:hover .box-icon {
  background-color: var(--primary-color);
}
.homeya-categories.style-1.active .box-icon .icon, .homeya-categories.style-1:hover .box-icon .icon {
  color: #ffffff;
  animation: 0.3s link-icon2 linear;
}
.homeya-categories.style-1.active .content p,
.homeya-categories.style-1.active .content h6, .homeya-categories.style-1:hover .content p,
.homeya-categories.style-1:hover .content h6 {
  color: #161e2d;
}
.homeya-categories.style-1::before {
  background-color: #ffffff;
}

.flat-categories-v2 {
  background: #161e2d;
}

.tf-sw-categories .sw-pagination {
  text-align: center;
  margin-top: 40px;
}
.tf-sw-categories .sw-pagination .swiper-pagination-bullet {
  background-color: #ffffff;
}

.flat-categories-v3 {
  padding: 0px 30px;
  border-bottom: 1px solid #e4e4e4;
  position: relative;
}

.homeya-categories-v2 {
  width: 155px;
  height: 107px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  transition: all 0.5s ease;
  position: relative;
}
.homeya-categories-v2 .icon-box .icon {
  transition: all 0.5s ease;
  font-size: 40px;
  color: #5c6368;
}
.homeya-categories-v2 .content {
  transition: all 0.5s ease;
  color: #5c6368;
}
.homeya-categories-v2::after {
  content: "";
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 4px;
  background-color: red;
  opacity: 0;
  transition: all 0.5s ease;
}
.homeya-categories-v2.active, .homeya-categories-v2:hover {
  border-color: var(--primary-color);
}
.homeya-categories-v2.active .icon-box .icon, .homeya-categories-v2:hover .icon-box .icon {
  color: #161e2d;
}
.homeya-categories-v2.active .content, .homeya-categories-v2:hover .content {
  color: #161e2d;
}
.homeya-categories-v2.active::after, .homeya-categories-v2:hover::after {
  opacity: 1;
}

.wrap-categories-v3 .navigation {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  z-index: 123;
  border-radius: 8px;
}
.wrap-categories-v3 .navigation .icon {
  font-size: 16px;
}
.wrap-categories-v3 .swiper-nav-next {
  left: 20px;
}
.wrap-categories-v3 .swiper-nav-prev {
  right: 20px;
}

.map-marker-container {
  position: absolute;
  margin-top: 10px;
  transform: translate3d(-50%, -100%, 0);
}

.marker-container {
  position: relative;
  width: 46px;
  height: 46px;
  z-index: 1;
  border-radius: 50%;
  cursor: pointer;
  -webkit-perspective: 1000;
}

.marker-card .face {
  position: absolute;
  width: 32px;
  height: 32px;
  backface-visibility: hidden;
  text-align: center;
  color: #fff;
  z-index: 100;
  background: var(--primary-color);
  border: 8px solid #fff;
  border-radius: 50%;
  box-sizing: content-box;
  background-clip: content-box;
  line-height: 46px;
  font-size: 24px;
}

.marker-card .face::before,
.marker-card .face::after {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  -ms-border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  transform: translate(-50%, -50%);
  -ms-box-shadow: 0 0 0 50px rgba(238, 103, 66, 0.1);
  -o-box-shadow: 0 0px 0 50px rgba(238, 103, 66, 0.1);
  box-shadow: 0px 0px 0px 20px rgba(238, 103, 66, 0.1);
  animation: ripple 2s infinite;
}

.marker-card .face::before {
  content: "";
  position: absolute;
  animation-delay: 0.6s;
}

.marker-card .face::after {
  content: "";
  position: absolute;
  animation-delay: 0.2s;
}

.marker-card .face > div {
  background-image: url(../../assets/images/section/bg-icon.jpg);
  position: absolute;
  height: 100%;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  z-index: 99;
}

#singleListingMap .marker-container {
  cursor: default;
}

#singleListingMap .marker-container {
  cursor: default;
}

.marker-card {
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  position: absolute;
  z-index: 1;
}

#map .infoBox {
  margin-left: 190px;
  margin-bottom: -120px;
}

.map-listing-item {
  position: relative;
}

.map-listing-item .infoBox-close {
  position: absolute;
  right: 8px;
  top: 8px;
  width: 24px;
  height: 24px;
  line-height: 24px;
  font-size: 12px;
  border-radius: 8px;
  z-index: 9;
  text-align: center;
  cursor: pointer;
  transition: all 300ms ease;
}

.map-listing-item .inner-box {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.map-listing-item .inner-box .image-box {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 4px;
  overflow: hidden;
}
.map-listing-item .inner-box .image-box img {
  display: block;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  transition: all 500ms ease;
}
.map-listing-item .inner-box .image-box .flag-tag {
  position: absolute;
  top: 0.25rem;
  inset-inline-end: 0.25rem;
  font-size: 10px;
}

.map-listing-item .content {
  position: relative;
}
.map-listing-item .content .location {
  color: #5c6368;
  font-size: 12px;
  line-height: 16px;
  display: flex;
  align-items: center;
  gap: 4px;
}
.map-listing-item .content .location .icon {
  width: 16px;
  height: 16px;
}
.map-listing-item .content .title {
  font-size: 18px;
  line-height: 28px;
  margin-top: 4px;
  font-weight: 700;
}
.map-listing-item .content .price {
  font-size: 14px;
  line-height: 20px;
  font-weight: 700;
  color: var(--primary-color);
  margin-top: 4px;
  text-align: start;
}
.map-listing-item .content .list-info {
  margin-top: 8px;
  display: flex;
  gap: 20px;
}
.map-listing-item .content .list-info li {
  font-weight: 600;
  font-size: 12px;
  line-height: 19px;
  letter-spacing: 0.8px;
  display: flex;
  gap: 4px;
}
.map-listing-item .content .list-info li .icon {
  width: 16px;
  height: 16px;
  color: #5c6368;
}

.cluster-map-visible {
  text-align: center;
  font-size: 16px !important;
  color: #ffffff !important;
  font-weight: 500 !important;
  border-radius: 50%;
  width: 40px !important;
  height: 40px !important;
  line-height: 40px !important;
  background-color: var(--primary-color);
  border: 8px solid rgba(238, 103, 66, 0.1);
  box-shadow: 0 7px 30px rgba(33, 33, 33, 0.3);
  box-sizing: content-box;
  background-clip: content-box;
}

.flat-map .top-map {
  height: 460px;
}
.flat-map .wrap-filter-search {
  margin-top: -3.25rem;
}

.wrap-banner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 20px;
}
.wrap-banner .box-left {
  padding: 60px 20px 60px 80px;
}
.wrap-banner .box-left .box-title {
  margin-bottom: 30px;
}
.wrap-banner .box-right {
  flex-shrink: 0;
  max-width: 60%;
}
.wrap-banner .box-right img {
  margin-top: -60px;
}

.wrapper-layout {
  display: flex;
  height: 100%;
}
.wrapper-layout .wrap-left {
  width: 54.7%;
  position: relative;
  height: calc(100vh - 188px);
  overflow-x: hidden;
  overflow-y: auto;
  padding: 24px 40px;
  padding-bottom: 0px;
}
.wrapper-layout .wrap-left::-webkit-scrollbar {
  width: 12px;
}
.wrapper-layout .wrap-left::-webkit-scrollbar-thumb {
  background: #e4e4e4;
}
.wrapper-layout .wrap-left .title {
  font-weight: 600;
  font-family: var(--heading-font);
  margin-bottom: 20px;
}
.wrapper-layout .wrap-right {
  width: 45.3%;
  position: absolute;
  height: calc(100vh - 188px);
  right: 0;
}
.wrapper-layout .wrap-right #map {
  position: absolute;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
}
.wrapper-layout .homeya-box {
  margin-bottom: 30px;
}
.wrapper-layout.layout-2 .wrap-left {
  height: calc(100vh - 178px);
  width: 47.4%;
  padding: 30px 30px 0;
}
.wrapper-layout.layout-2 .wrap-left::-webkit-scrollbar {
  width: 8px;
}
.wrapper-layout.layout-2 .wrap-left::-webkit-scrollbar-thumb {
  background: #e4e4e4;
}
.wrapper-layout.layout-2 .wrap-right {
  height: calc(100vh - 11.1rem);
  width: 52.6%;
}

.wrapper-layout-3 {
  display: flex;
  height: 100%;
}
.wrapper-layout-3 .wrap-sidebar {
  height: calc(100vh - 80px);
  width: 23.3%;
  padding-bottom: 80px;
  overflow-x: hidden;
  overflow-y: auto;
  background-color: #f7f7f7;
}
.wrapper-layout-3 .wrap-sidebar::-webkit-scrollbar {
  width: 8px;
}
.wrapper-layout-3 .wrap-sidebar::-webkit-scrollbar-thumb {
  background: #5c6368;
}
.wrapper-layout-3 .wrap-sidebar .widget-filter-search {
  padding: 30px;
}
.wrapper-layout-3 .wrap-inner {
  width: 47.3%;
  padding: 30px 30px 0;
  height: calc(100vh - 80px);
  overflow-x: hidden;
  overflow-y: auto;
}
.wrapper-layout-3 .wrap-inner::-webkit-scrollbar {
  width: 8px;
}
.wrapper-layout-3 .wrap-inner::-webkit-scrollbar-thumb {
  background: #5c6368;
}
.wrapper-layout-3 .wrap-map {
  height: calc(100vh - 80px);
  width: 29.4%;
  right: 0;
  bottom: 0;
  position: fixed;
}
.wrapper-layout-3 .wrap-map #map {
  position: absolute;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
}
.wrapper-layout-3 .homeya-box {
  margin-bottom: 30px;
}

.box-title-listing {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40px;
  flex-wrap: wrap;
  gap: 15px;
}
.box-title-listing .box-filter-tab {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}
.box-title-listing .box-filter-tab .nice-select {
  padding: 10px 63px 10px 16px;
}
.box-title-listing .box-filter-tab .list-sort {
  width: 100%;
  height: 48px;
}
.box-title-listing .box-filter-tab .list-page {
  width: 160px;
  height: 48px;
}
.box-title-listing.style-1 {
  margin-bottom: 30px;
}

.wd-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
}
.wd-navigation .nav-item {
  width: 48px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  border-radius: 8px;
}
.wd-navigation .nav-item:hover, .wd-navigation .nav-item.active {
  background-color: var(--primary-color);
  color: #ffffff;
}

.widget-sidebar .widget-box:not(:last-child) {
  margin-bottom: 30px;
}

.box-latest-property .title {
  margin-bottom: 20px;
}
.box-latest-property .latest-property-item:not(:last-child) {
  padding-bottom: 24px;
  margin-bottom: 24px;
  border-bottom: 1px solid #e4e4e4;
}

.latest-property-item {
  display: flex;
  align-items: center;
  gap: 16px;
}
.latest-property-item .images-style {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  width: 110px;
  height: 110px;
}
.latest-property-item .images-style img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  transition: all 0.3s ease;
}
.latest-property-item .images-style::after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5019607843);
  opacity: 0;
  transition: all 0.3s ease;
}
.latest-property-item .meta-list {
  display: flex;
  align-items: center;
  gap: 20px;
  margin: 8px 0px;
}
.latest-property-item:hover .images-style img {
  transform: scale(1.05);
}
.latest-property-item:hover .images-style::after {
  opacity: 1;
}

.fixed-sidebar {
  position: sticky;
  top: 100px;
}

.fixed-sidebar-2 {
  position: sticky;
  top: 140px;
}

.fixed-header { /* Safari */
  position: sticky;
  top: 0;
}

.fixed-cate-single {
  position: sticky;
  top: 80px;
  z-index: 50;
}

.flat-slider-detail-v1 {
  padding: 0 !important;
  position: relative;
  margin-bottom: -7%;
}
.flat-slider-detail-v1 .navigation.swiper-nav-next {
  left: 40px;
}
.flat-slider-detail-v1 .navigation.swiper-nav-prev {
  right: 40px;
}
.flat-slider-detail-v1 .icon-box {
  position: absolute;
  display: flex;
  align-items: center;
  gap: 10px;
  right: 36px;
  top: 20px;
  z-index: 12;
}
.flat-slider-detail-v1 .icon-box .item {
  border-radius: 8px;
  width: 52px;
  height: 52px;
  transition: all 0.5s ease;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.flat-slider-detail-v1 .icon-box .item .icon {
  transition: all 0.5s ease;
  color: #161e2d;
  font-size: 32px;
}
.flat-slider-detail-v1 .icon-box .item:hover, .flat-slider-detail-v1 .icon-box .item.active {
  background-color: var(--primary-color);
}
.flat-slider-detail-v1 .icon-box .item:hover .icon, .flat-slider-detail-v1 .icon-box .item.active .icon {
  color: #ffffff;
}

.flat-property-detail .header-property-detail {
  position: relative;
  z-index: 15;
  margin: 0px -30px 20px;
}

.header-property-detail {
  padding: 30px;
  border-radius: 16px;
  background-color: #ffffff;
}
.header-property-detail .content-top {
  padding-bottom: 23px;
  margin-bottom: 23px;
  border-bottom: 1px solid #e4e4e4;
  flex-wrap: wrap;
  gap: 20px;
}
.header-property-detail .content-top .title {
  margin-top: 8px;
}
.header-property-detail .content-top .flag-tag:hover {
  background-color: var(--primary-color);
}
.header-property-detail .content-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
}
.header-property-detail .content-bottom .info-box .label {
  margin-bottom: 12px;
  letter-spacing: 0.8px;
  font-weight: 700;
  color: #5c6368;
  opacity: 0.8;
}
.header-property-detail .content-bottom .info-box .meta {
  display: flex;
  align-items: center;
  gap: 30px;
  flex-wrap: wrap;
}
.header-property-detail .content-bottom .info-box .meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  line-height: 28px;
  font-weight: 700;
  color: #161e2d;
}
.header-property-detail .content-bottom .info-box .meta-item .meta-item-review {
  display: flex;
  align-items: center;
}
.header-property-detail .content-bottom .info-box .meta-item .meta-item-review .icon {
  width: 1.25rem;
  height: 1.25rem;
}
.header-property-detail .content-bottom .info-box .meta-item .icon {
  width: 28px;
  height: 28px;
  color: var(--primary-color);
}
.header-property-detail .content-bottom .icon-box {
  display: flex;
  gap: 16px;
}
.header-property-detail .content-bottom .icon-box .item {
  width: 52px;
  height: 52px;
  background-color: #f7f7f7;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e4e4e4;
  border-radius: 4px;
}
.header-property-detail .content-bottom .icon-box .item .icon {
  width: 28px;
  height: 28px;
  color: #5c6368;
}
.header-property-detail .content-bottom .icon-box .item:hover {
  background-color: var(--primary-color);
}
.header-property-detail .content-bottom .icon-box .item:hover .icon {
  animation: 0.3s link-icon2 linear;
  color: #ffffff;
}

.single-property-element:not(:last-child) {
  padding-bottom: 40px;
  margin-bottom: 40px;
  border-bottom: 1px solid #e4e4e4;
}

.single-property-desc .title {
  margin-bottom: 16px;
}
.single-property-desc .btn-view {
  margin-top: 16px;
}
.single-property-desc .btn-view .text::before {
  background-color: #161e2d;
}

.single-property-overview .title {
  margin-bottom: 16px;
}
.single-property-overview .info-box .item {
  display: flex;
  align-items: center;
  gap: 12px;
}
.single-property-overview .info-box .item span {
  font-weight: 700;
}
.single-property-overview .info-box .item .label {
  color: #5c6368;
  font-weight: 400;
  display: block;
  opacity: 0.8;
}
.single-property-overview .info-box .item .box-icon {
  border-radius: 8px;
  background-color: #f7f7f7;
  min-width: 52px;
}
.single-property-overview .info-box .item .box-icon .icon {
  height: 28px;
  width: 28px;
  color: var(--primary-color);
}
.single-property-overview .info-box .item:hover .box-icon {
  background-color: var(--primary-color);
}
.single-property-overview .info-box .item:hover .box-icon .icon {
  color: #ffffff;
  animation: 0.3s link-icon2 linear;
}

.single-property-video {
  padding-right: 55px;
}
.single-property-video .title {
  margin-bottom: 20px;
}
.single-property-video .img-video {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
}
.single-property-video .img-video img {
  width: 100%;
}
.single-property-video .img-video .btn-video {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  background-color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.single-property-video .img-video .btn-video .icon {
  color: var(--primary-color);
  font-size: 26px;
}

.single-property-info {
  padding-bottom: 32px;
}
.single-property-info .title {
  margin-bottom: 16px;
}
.single-property-info .inner-box {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.single-property-info .inner-box .label {
  color: #5c6368;
  opacity: 0.8;
  width: 32%;
}

.single-property-feature .title {
  margin-bottom: 16px;
}
.single-property-feature .box-feature ul {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  -moz-column-gap: 40px;
       column-gap: 40px;
  row-gap: 8px;
}
.single-property-feature .box-feature .feature-item {
  display: flex;
  align-items: center;
  color: #5c6368;
  gap: 8px;
}
.single-property-feature .box-feature .feature-item .icon {
  font-size: 20px;
}

.single-property-map .title {
  margin-bottom: 20px;
}
.single-property-map .info-map {
  margin-top: 20px;
  display: flex;
  gap: 80px;
  flex-wrap: wrap;
}

.map-single {
  height: 364px;
  border-radius: 16px;
}
.map-single .marker-card .face {
  background: none;
  border: none;
}
.map-single .marker-card .face::before, .map-single .marker-card .face::after {
  content: none;
}
.map-single .marker-card .face div {
  background-image: url("../images/location/map-icon.png");
  width: 60px;
  height: 60px;
}

.single-property-floor .title {
  margin-bottom: 20px;
}
.single-property-floor .box-floor .floor-item:not(:last-child) {
  margin-bottom: 20px;
}
.single-property-floor .floor-item {
  padding: 0px 20px;
  background-color: #f7f7f7;
  border-radius: 12px;
}
.single-property-floor .floor-item .floor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 15px;
  padding: 17px 0px;
}
.single-property-floor .floor-item .floor-header .inner-left {
  display: flex;
  align-items: center;
  gap: 12px;
}
.single-property-floor .floor-item .floor-header .inner-left .icon {
  margin-top: -2px;
  display: inline-block;
  transform: rotate(90deg);
  font-size: 14px;
  transition: all 0.3s ease;
}
.single-property-floor .floor-item .floor-header .inner-right {
  display: flex;
  gap: 30px;
}
.single-property-floor .floor-item .floor-header .inner-right .icon {
  font-size: 24px;
}
.single-property-floor .floor-item .floor-header:not(.collapsed) .inner-left .icon {
  transform: rotate(-90deg);
}
.single-property-floor .floor-item .faq-body {
  padding: 17px 0px;
  border-top: 1px solid #e4e4e4;
}
.single-property-floor .floor-item .faq-body .box-img {
  padding: 20px 30px;
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
}

.single-property-attachments .title {
  margin-bottom: 20px;
}
.single-property-attachments .attachments-item {
  display: flex;
  align-items: center;
  gap: 12px;
}
.single-property-attachments .attachments-item .box-icon {
  border-radius: 8px;
  background-color: #f7f7f7;
}
.single-property-attachments .attachments-item .icon {
  font-size: 24px;
}

.single-property-explore .title {
  margin-bottom: 20px;
}
.single-property-explore .box-img {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
}
.single-property-explore .box-img .box-icon {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.single-property-explore .box-img .box-icon .icon {
  display: inline-block;
  font-size: 48px;
  transition: all 0.5s ease;
  animation: rotate1 5s infinite ease-in-out;
}

.single-property-nearby .title {
  margin-bottom: 16px;
}
.single-property-nearby .box-nearby {
  margin-top: 16px;
}
.single-property-nearby .box-nearby .item-nearby {
  display: flex;
  align-items: center;
}
.single-property-nearby .box-nearby .item-nearby:not(:last-child) {
  margin-bottom: 8px;
}
.single-property-nearby .item-nearby {
  display: flex;
}
.single-property-nearby .item-nearby .label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #5c6368;
  opacity: 0.8;
  margin-inline-end: 10px;
}

@keyframes rotate1 {
  from {
    transform: rotate(-360deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.single-property-loan .title {
  margin-bottom: 20px;
}
.single-property-loan .box-loan-calc {
  border-radius: 16px;
  background-color: #f7f7f7;
}
.single-property-loan .box-loan-calc .box-top {
  padding: 20px;
  padding-right: 40px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  row-gap: 20px;
  -moz-column-gap: 30px;
       column-gap: 30px;
}
.single-property-loan .box-loan-calc .item-calc .label {
  color: #5c6368;
  margin-bottom: 8px;
}
.single-property-loan .box-loan-calc .box-bottom {
  border-top: 1px solid #e4e4e4;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.single-property-loan .box-loan-calc .form-control {
  padding: 10px 16px;
}

.single-wrapper-review .box-title-review {
  margin-bottom: 20px;
}
.single-wrapper-review .wrap-review {
  padding-top: 40px;
  margin-top: 20px;
  border-top: 1px solid #e4e4e4;
}

.flat-latest-property .box-title {
  margin-bottom: 30px;
}

.wrapper-sidebar-right {
  padding-inline-start: 30px;
}

.single-property-contact .title {
  margin-bottom: 20px;
}
.single-property-contact .box-avatar {
  display: flex;
  align-items: center;
  gap: 20px;
}
.single-property-contact .box-avatar .name {
  margin-bottom: 8px;
}
.single-property-contact .box-avatar .info {
  display: flex;
  flex-direction: column;
}
.single-property-contact .box-avatar .info-item {
  color: #5c6368;
}
.single-property-contact .contact-form {
  margin-top: 20px;
}
.single-property-contact .contact-form .ip-group label {
  margin-bottom: 8px;
}
.single-property-contact .contact-form .ip-group .form-control {
  padding: 10px 16px;
}
.single-property-contact .contact-form .ip-group textarea {
  height: 100px;
}
.single-property-contact .contact-form .ip-group:not(:last-child) {
  margin-bottom: 12px;
}
.single-property-contact .contact-form .ip-group .tf-btn {
  margin-top: 20px;
}
.single-property-contact .textarea-group {
  margin-top: 30px;
}

.single-property-whychoose .title {
  margin-bottom: 16px;
}
.single-property-whychoose .box-whychoose .item-why {
  display: flex;
  align-items: center;
  gap: 8px;
}
.single-property-whychoose .box-whychoose .item-why .icon {
  font-size: 24px;
}
.single-property-whychoose .box-whychoose .item-why:not(:last-child) {
  margin-bottom: 12px;
}

.flat-property-detail-v2 .wrapper-onepage:not(:last-child) {
  margin-bottom: 30px;
}

.flat-categories-single {
  border-bottom: 1px solid #e4e4e4;
}
.flat-categories-single .cate-single-tab {
  overflow-x: auto;
  display: flex;
  margin-right: -15px;
  padding-right: 15px;
}
.flat-categories-single .cate-single-tab::-webkit-scrollbar {
  width: 1px;
  height: 1px;
}
.flat-categories-single .cate-single-tab::-webkit-scrollbar-thumb {
  background: transparent;
}
.flat-categories-single .cate-single-tab .cate-single-item {
  flex-shrink: 0;
  width: 143px;
  height: 48px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s ease;
  border-bottom: 2px solid transparent;
}
.flat-categories-single .cate-single-tab li:hover .cate-single-item, .flat-categories-single .cate-single-tab li.active .cate-single-item {
  border-color: var(--primary-color);
}

.widget-box-header-single {
  background-color: #ffffff;
  border-radius: 16px;
  margin-bottom: 30px;
}
.widget-box-header-single .header-property-detail {
  padding: 0;
}
.widget-box-header-single .header-property-detail .content-top {
  padding: 30px 30px 20px;
  margin-bottom: 20px;
}
.widget-box-header-single .header-property-detail .content-bottom {
  padding: 0px 30px 40px;
}
.widget-box-header-single .single-property-desc {
  padding: 0px 30px 40px;
  border-bottom: 1px solid #e4e4e4;
}
.widget-box-header-single .single-property-overview {
  padding: 40px 30px;
}

.widget-box-single {
  padding: 30px;
  border-radius: 16px;
  background-color: #ffffff;
}
.widget-box-single.single-property-info {
  margin-bottom: 30px;
}

.single-property-loan-v2 .title {
  margin-bottom: 12px;
}
.single-property-loan-v2 .item-calc:not(:last-child) {
  margin-bottom: 12px;
}
.single-property-loan-v2 .item-calc label {
  margin-bottom: 8px;
  color: #5c6368;
}
.single-property-loan-v2 .item-calc input {
  padding: 10px 16px;
}
.single-property-loan-v2 .box-bottom {
  margin-top: 20px;
}
.single-property-loan-v2 .box-bottom .tf-btn {
  width: 100%;
  margin-bottom: 16px;
}

.wrapper-sidebar-right .box-latest-property {
  padding-right: 0px;
}

.flat-gallery-single {
  padding: 20px 20px 0px;
  display: grid;
  grid-template-areas: "item1 item1 item2 item3" "item1 item1 item4 item5";
  gap: 20px;
}
.flat-gallery-single .item1 {
  grid-area: item1;
  position: relative;
}
.flat-gallery-single .item1 .box-btn {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 12px;
}
.flat-gallery-single .item1 .box-btn .box-icon {
  width: 48px;
  height: 48px;
  border-radius: 4px;
  background-color: #ffffff;
  font-size: 28px;
}
.flat-gallery-single .item2 {
  grid-area: item2;
}
.flat-gallery-single .item3 {
  grid-area: item3;
}
.flat-gallery-single .item4 {
  grid-area: item4;
}
.flat-gallery-single .item5 {
  grid-area: item5;
}
.flat-gallery-single .box-img {
  border-radius: 16px;
  overflow: hidden;
}
.flat-gallery-single .box-img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.banner-property-2 img {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.flat-property-detail-v3 .header-property-detail {
  padding: 0;
  margin-bottom: 40px;
}
.flat-property-detail-v3 .header-property-detail .content-top {
  padding-bottom: 20px;
  margin-bottom: 20px;
}

.thumbs-sw-pagi {
  margin-top: 16px;
}
.thumbs-sw-pagi .swiper-slide {
  width: auto;
}
.thumbs-sw-pagi .img-thumb-pagi {
  border-radius: 8px;
  overflow: hidden;
}

.single-property-gallery .image-sw-single {
  border-radius: 16px;
  overflow: hidden;
}
.single-property-gallery .box-navigation .navigation {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 123;
  border-color: transparent;
  background-color: #f7f7f7;
}
.single-property-gallery .box-navigation .navigation.swiper-nav-next {
  left: -35px;
}
.single-property-gallery .box-navigation .navigation.swiper-nav-prev {
  right: -35px;
}
.single-property-gallery .box-navigation .navigation:hover, .single-property-gallery .box-navigation .navigation.swiper-button-disabled {
  background-color: var(--primary-color);
}
.single-property-gallery .box-navigation .navigation:hover .icon, .single-property-gallery .box-navigation .navigation.swiper-button-disabled .icon {
  color: #ffffff;
}

.flat-property-detail-v4 .header-property-detail {
  padding: 0px;
  margin-bottom: 60px;
}
.flat-property-detail-v4 .single-property-gallery {
  margin-bottom: 60px;
}
.flat-property-detail-v4 .single-property-video {
  padding-right: 0;
}
.flat-property-detail-v4 .single-property-map .map-single {
  height: 552px;
}
.flat-property-detail-v4 .single-property-feature .wrap-feature {
  justify-content: space-between;
}

.content-box-privacy {
  margin-top: 40px;
}
.content-box-privacy p {
  margin-top: 12px;
  font-size: 18px;
  line-height: 28px;
  color: #5c6368;
}
.content-box-privacy .box-list {
  margin-top: 12px;
}
.content-box-privacy .box-list li {
  margin-top: 12px;
  display: flex;
  font-size: 18px;
  line-height: 28px;
}
.content-box-privacy .box-list li::before {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: #64666c;
  content: "";
  display: block;
  margin-left: 9px;
  margin-right: 10px;
  margin-top: 12px;
  flex-shrink: 0;
}

.flat-section .tf-faq:not(:last-child) {
  margin-bottom: 60px;
}

.tf-faq h5 {
  margin-bottom: 30px;
}

.box-faq .faq-item {
  border: 1px solid #e4e4e4;
  border-radius: 12px;
  padding: 0px 20px;
}
.box-faq .faq-item .faq-header {
  padding: 20px 0px;
  padding-right: 30px;
  display: block;
  font-size: 20px;
  line-height: 28px;
  font-weight: 700;
  position: relative;
}
.box-faq .faq-item .faq-header::after {
  position: absolute;
  content: "\e917";
  font-family: "icomoon";
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.5s ease;
  font-size: 24px;
}
.box-faq .faq-item .faq-header:not(.collapsed)::after {
  content: "\e916";
}
.box-faq .faq-item .faq-body {
  border-top: 1px solid #e4e4e4;
  padding: 12px 0px 20px;
  color: #5c6368;
  font-size: 18px;
  line-height: 28px;
}
.box-faq .faq-item:not(:last-child) {
  margin-bottom: 12px;
}

.box-pricing {
  padding: 30px;
  border-radius: 16px;
  background-color: #f7f7f7;
  transition: all 0.5s ease;
}
.box-pricing .price {
  margin-bottom: 20px;
}
.box-pricing .box-title-price {
  margin-bottom: 20px;
}
.box-pricing .box-title-price .title {
  margin-bottom: 8px;
}
.box-pricing .box-title-price .title h4,
.box-pricing .box-title-price .title span {
  transition: all 0.5s ease;
}
.box-pricing .box-title-price .desc {
  font-size: 16px;
  line-height: 26px;
  color: #5c6368;
  transition: all 0.5s ease;
}
.box-pricing .list-price {
  margin-bottom: 20px;
}
.box-pricing .list-price .item {
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.5s ease;
}
.box-pricing .list-price .item:not(:last-child) {
  margin-bottom: 8px;
}
.box-pricing .list-price .check-icon {
  transition: all 0.5s ease;
}
.box-pricing .tf-btn {
  width: 100%;
}
.box-pricing.active, .box-pricing:hover {
  background-color: var(--primary-color);
}
.box-pricing.active .tf-btn, .box-pricing:hover .tf-btn {
  background-color: #ffffff;
  color: #161e2d;
  border-color: #ffffff;
}
.box-pricing.active h4,
.box-pricing.active span,
.box-pricing.active h6,
.box-pricing.active .desc,
.box-pricing.active li, .box-pricing:hover h4,
.box-pricing:hover span,
.box-pricing:hover h6,
.box-pricing:hover .desc,
.box-pricing:hover li {
  color: #ffffff;
}
.box-pricing.active .check-icon, .box-pricing:hover .check-icon {
  background-color: #ffffff;
  color: var(--primary-color);
}
.box-pricing.active {
  padding-top: 60px;
  padding-bottom: 40px;
  position: relative;
}
.box-pricing.active .tag {
  position: absolute;
  color: #161e2d;
  right: 20px;
  top: 20px;
}
.box-pricing .tag {
  padding: 4px 8px;
  border-radius: 4px;
  background-color: #ffffff;
  font-size: 12px;
  line-height: 19px;
  letter-spacing: 0.8px;
  font-weight: 600;
  font-family: var(--heading-font);
}

.check-icon {
  width: 20px;
  height: 20px;
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  border-radius: 50%;
  color: #ffffff;
}
.check-icon.disable {
  background-color: #e4e4e4;
  color: #161e2d;
}

.flat-banner-about .btn-view {
  margin-top: 10px;
}
.flat-banner-about .banner-video {
  margin-top: 40px;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
}
.flat-banner-about .banner-video .btn-video {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border-radius: 50%;
}
.flat-banner-about .banner-video .btn-video .icon {
  color: var(--primary-color);
  font-size: 100px;
}

.flat-section .wrap-partner {
  margin-top: 40px;
}

.flat-slider-contact {
  position: relative;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  z-index: 123;
  background-image: url(../images/slider/slider-contact.jpg);
  background-attachment: fixed;
}
.flat-slider-contact .overlay {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #161e2d;
  opacity: 0.8;
}
.flat-slider-contact .content-wrap {
  position: relative;
  z-index: 1;
  align-items: center;
}
.flat-slider-contact .content-wrap .box-title {
  margin-bottom: 20px;
}
.flat-slider-contact .content-wrap .content-left {
  padding-right: 210px;
}

.box-contact-v2 {
  padding: 40px;
  border-radius: 16px;
  box-shadow: 0px 10px 25px 0px rgba(54, 95, 104, 0.**********);
  background-color: #ffffff;
}
.box-contact-v2 textarea {
  height: 100px;
}
.box-contact-v2 .box .label {
  color: #5c6368;
  margin-bottom: 8px;
}
.box-contact-v2 .box .form-control:focus {
  border: 2px solid #161e2d !important;
}
.box-contact-v2 .box:not(:last-child) {
  margin-bottom: 20px;
}

.flat-contact .contact-content {
  padding-right: 110px;
}
.flat-contact .contact-content h5 {
  margin-bottom: 12px;
}
.flat-contact .contact-content p {
  margin-bottom: 30px;
}
.flat-contact .contact-content .form-contact .box {
  gap: 30px;
}
.flat-contact .contact-content .form-contact .box label {
  margin-bottom: 8px;
}
.flat-contact .contact-content .form-contact .box:not(:last-child) {
  margin-bottom: 20px;
}
.flat-contact .contact-content .form-contact .tf-btn {
  margin-top: 10px;
}

.contact-info {
  padding: 30px;
  border-radius: 16px;
  background-color: #f7f7f7;
}
.contact-info h5 {
  margin-bottom: 20px;
}
.contact-info .box .title {
  margin-bottom: 8px;
}
.contact-info .box:not(:last-child) {
  margin-bottom: 24px;
}
.contact-info .box .box-social {
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}
.contact-info .box .box-social .item {
  width: 44px;
  height: 44px;
  border-radius: 8px;
  border: 1px solid #e4e4e4;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.contact-info .box .box-social .item svg path {
  transition: all 0.3s ease;
}
.contact-info .box .box-social .item:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}
.contact-info .box .box-social .item:hover svg path {
  fill: #ffffff;
}

.map-contact {
  height: 600px;
  border-radius: 16px;
}
.map-contact .marker-card .face {
  background: none;
  border: none;
}
.map-contact .marker-card .face::before, .map-contact .marker-card .face::after {
  content: none;
}
.map-contact .marker-card .face div {
  background-image: url("../images/location/map-lo.png");
  width: 60px;
  height: 60px;
}

.flat-account {
  padding: 60px;
  border-radius: 24px;
}
.flat-account .title {
  margin-bottom: 24px;
}
.flat-account .box-fieldset:not(:last-child) {
  margin-bottom: 20px;
}
.flat-account .auth-line {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 24px 0px;
}
.flat-account .auth-line::after, .flat-account .auth-line::before {
  position: absolute;
  content: "";
  height: 1px;
  width: 36%;
  background-color: #e4e4e4;
}
.flat-account .auth-line::before {
  left: 0;
}
.flat-account .auth-line::after {
  right: 0;
}
.flat-account .login-social .btn-login-social:not(:last-child) {
  margin-bottom: 12px;
}
.flat-account .btn-login-social {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-weight: 700;
  height: 48px;
  border: 1px solid #e4e4e4;
  border-radius: 8px;
  background-color: #ffffff;
}
.flat-account .btn-login-social img {
  width: 24px;
}
.flat-account .btn-login-social:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: #ffffff;
}
.flat-account .tf-btn {
  margin-top: 24px;
}
.flat-account .noti a {
  margin-inline-start: 4px;
  border-bottom: 1px solid #161e2d;
}

.box-password {
  position: relative;
}
.box-password .form-control {
  padding-right: 40px;
}
.box-password .show-pass,
.box-password .show-pass2,
.box-password .show-pass3 {
  position: absolute;
  right: 16px;
  top: 16px;
  cursor: pointer;
}
.box-password .show-pass .icon-eye,
.box-password .show-pass2 .icon-eye,
.box-password .show-pass3 .icon-eye {
  display: none;
}
.box-password .show-pass .icon-pass,
.box-password .show-pass2 .icon-pass,
.box-password .show-pass3 .icon-pass {
  font-size: 20px;
  color: #5c6368;
}
.box-password .show-pass.active .icon-eye,
.box-password .show-pass2.active .icon-eye,
.box-password .show-pass3.active .icon-eye {
  display: inline-block;
}
.box-password .show-pass.active .icon-eye-off,
.box-password .show-pass2.active .icon-eye-off,
.box-password .show-pass3.active .icon-eye-off {
  display: none;
}

.modal .modal-dialog .modal-content {
  border-radius: 24px;
  border: 0;
  background: #f7f7f7;
}

.modal .flat-account {
  position: relative;
}
.modal .flat-account .close-modal {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 18px;
  cursor: pointer;
}

.modal-backdrop {
  background-color: rgba(22, 30, 45, 0.3);
}
.modal-backdrop.show {
  opacity: 1;
}

.progress-wrap {
  position: fixed;
  bottom: 30px;
  inset-inline-end: 30px;
  height: 44px;
  width: 44px;
  cursor: pointer;
  display: block;
  border-radius: 50%;
  box-shadow: 0px 10px 25px 0px rgba(54, 95, 104, 0.**********);
  z-index: 100;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: all 400ms linear;
  background: #ffffff;
}
.progress-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}
.progress-wrap::after {
  position: absolute;
  content: "\ea3d";
  font-family: "icomoon";
  text-align: center;
  line-height: 44px;
  font-size: 14px;
  font-weight: 900;
  color: var(--primary-color);
  left: 0;
  top: 0;
  height: 44px;
  width: 44px;
  cursor: pointer;
  display: block;
  z-index: 1;
  transform: rotate(223deg);
  transition: all 400ms linear;
}
.progress-wrap svg path {
  fill: #ffffff;
  box-sizing: border-box;
  stroke: var(--primary-color);
  stroke-width: 5;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  transition-duration: 0.4s;
  transition-timing-function: linear;
}

.msg-success {
  color: #198754;
}
.msg-success .close {
  font-size: 12px;
  margin-left: 10px;
  color: #198754;
}

#subscribe-msg .notification_ok {
  color: #198754;
}

.apartment-swiper .apartment-swiper-button-next,
.apartment-swiper .apartment-swiper-button-prev {
  opacity: 0;
  visibility: hidden;
}
.apartment-swiper:hover .apartment-swiper-button-next,
.apartment-swiper:hover .apartment-swiper-button-prev {
  opacity: 1;
  visibility: visible;
}

/*-------------- Responsive ----------------- */
/* Media Queries
-------------------------------------------------------------- */
@media only screen and (min-width: 1520px) {
  .tf-sw-testimonial:hover {
    cursor: url("../images/avatar/cursor-sw.png"), auto;
  }
  .wrap-categories {
    position: relative;
  }
  .wrap-categories .navigation {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }
  .wrap-categories .navigation.swiper-nav-next {
    left: -65px;
  }
  .wrap-categories .navigation.swiper-nav-prev {
    right: -65px;
  }
  .wrap-sw-property {
    position: relative;
  }
  .wrap-sw-property .navigation {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 123;
  }
  .wrap-sw-property .navigation.swiper-nav-next {
    left: -30px;
  }
  .wrap-sw-property .navigation.swiper-nav-prev {
    right: -30px;
  }
}
@media only screen and (min-width: 991px) {
  .flat-property-detail-v4 .single-property-video .btn-video {
    width: 80px;
    height: 80px;
  }
  .flat-property-detail-v4 .single-property-video .btn-video .icon {
    font-size: 42px;
  }
  .flat-property-detail-v4 .single-property-feature .wrap-feature {
    padding-right: 100px;
  }
  .flat-slider .heading br {
    display: none;
  }
}
@media only screen and (max-width: 1800px) {
  .layout-wrap .wrap-table table tbody td:nth-child(3) {
    padding-left: 20px;
  }
  .layout-wrap .main-content-inner {
    padding: 80px 15px;
  }
}
@media only screen and (max-width: 1520px) {
  .main-header .main-menu {
    position: relative;
    margin-left: 1px;
  }
  .wrap-categories .box-navigation {
    margin-top: 20px;
  }
  .wrap-sw-property .box-navigation {
    margin-top: 30px;
  }
  .wrapper-layout-3 {
    flex-wrap: wrap;
  }
  .wrapper-layout-3 .wrap-map {
    position: relative;
    height: 600px;
    width: 100%;
    margin-top: 30px;
  }
  .wrapper-layout-3 .wrap-inner {
    width: 70%;
  }
  .wrapper-layout-3 .wrap-sidebar {
    width: 30%;
    padding-bottom: 0px;
  }
  .wrapper-layout-3 .wrap-sidebar .widget-filter-search .form-btn-fixed {
    margin-top: 12px;
    padding: 0;
    background-color: transparent;
    position: unset;
    width: 100%;
  }
  .overlay .swiper-slide:not(.swiper-slide-prev, .swiper-slide-active, .swiper-slide-next)::after {
    content: none;
  }
}
@media only screen and (max-width: 1440px) {
  .show-mb {
    display: inline-block;
  }
  .header-dashboard .logo-box .button-show-hide {
    display: none;
  }
  .wrap-property-v2 .box-inner-right .content-property .pricing-property {
    gap: 15px;
    justify-content: space-between;
    flex-wrap: wrap;
  }
  .flat-property-detail .header-property-detail {
    margin-left: 0;
    margin-right: 0;
  }
  .flat-property-detail-v2 .wrapper-sidebar-right {
    padding-left: 0;
  }
  .single-property-contact .box-avatar {
    flex-wrap: wrap;
  }
  .single-property-gallery .box-navigation .navigation.swiper-nav-prev {
    right: 0;
  }
  .single-property-gallery .box-navigation .navigation.swiper-nav-next {
    left: 0;
  }
  .overlay-dashboard {
    position: fixed;
    right: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background: rgba(22, 30, 45, 0.3);
    transition: all 0.3s ease 0s;
    visibility: hidden;
    opacity: 0;
  }
  .layout-wrap .sidebar-menu-dashboard {
    transform: translateX(-100%);
  }
  .layout-wrap .main-content {
    padding-left: 0;
  }
  .layout-wrap.full-width .sidebar-menu-dashboard {
    transform: translateX(0);
  }
  .layout-wrap.full-width .overlay-dashboard {
    opacity: 1;
    visibility: visible;
    transition: all 0.3s ease;
  }
}
@media only screen and (max-width: 1350px) {
  .flat-testimonial {
    padding-left: 15px;
    padding-right: 15px;
  }
  .wrap-service-v2 .box-left {
    padding-right: 0px;
  }
  .wrap-service-v2 .box-left .list-view li:last-child, .wrap-service-v2 .box-left .list-view li:nth-child(2) {
    padding-left: 0px;
  }
  .wrap-service-v2 .box-right {
    padding-left: 25px;
    padding-right: 25px;
  }
  .header-dashboard .logo-box {
    gap: 20px;
  }
  .header-dashboard .header-account .box-avatar .name {
    display: none;
  }
}
@media only screen and (max-width: 1300px) {
  .wrapper-layout {
    flex-direction: column;
  }
  .wrapper-layout .wrap-right {
    position: relative;
    width: 100% !important;
    height: 600px !important;
  }
  .wrapper-layout .wrap-left {
    height: auto !important;
    width: 100% !important;
  }
  .wrap-property-v2 .box-inner-right {
    padding: 40px;
  }
  .wrap-property-v2 .box-inner-right .content-property .list-info {
    gap: 30px;
  }
  .wrap-service-v4 .inner-service-right {
    padding-right: 0;
  }
  .wrap-banner .box-right img {
    margin-top: 0px;
  }
  .header-style-3 .nav-outer {
    padding-left: 20px;
  }
  .flat-filter-search-v2 .flat-tab-form {
    flex-direction: column;
    gap: 20px;
  }
}
@media only screen and (max-width: 1199px) {
  .flat-slider.home-3 .slider-content .heading {
    padding-right: 10%;
  }
  .flat-slider.home-5 .thumbs-swiper-column1 {
    right: 15px;
  }
  .flat-slider.home-5 .thumbs-swiper-column .box-img {
    height: 700px;
  }
  .flat-slider.home-5 .thumbs-swiper-column .box-img img {
    -o-object-fit: cover;
       object-fit: cover;
    height: 100%;
  }
  .main-header .main-menu .navigation > li {
    padding-right: 24px;
  }
  .header-account ul {
    margin-right: 6px;
  }
  .main-header .main-menu .navigation > li.dropdown2 > a::after {
    font-size: 12px;
    right: -12px;
  }
  .wrap-counter {
    flex-wrap: wrap;
    gap: 30px;
  }
  .wrap-property {
    flex-wrap: wrap;
  }
  .wrap-property .box-left {
    flex-grow: 1;
  }
  .wrap-service-v2 .box-left {
    padding-right: 0px;
  }
  .wrap-service-v2 .box-left .list-view {
    display: flex;
    flex-wrap: wrap;
    row-gap: 12px;
  }
  .wrap-service-v2 .box-left .list-view li {
    width: 100%;
  }
  .wrap-service-v2 .box-service {
    flex-direction: column;
  }
  .wrap-service-v2 .box-service .content {
    text-align: center;
  }
  .flat-agents-v2 .grid-2,
  .flat-property-v2 .grid-2 {
    grid-template-columns: 1fr;
  }
  .grid-location-v2 {
    grid-template-columns: 1fr 1fr;
  }
  .homeya-box.style-3 .images-group .images-style {
    height: 100%;
  }
  .wrap-banner .box-left {
    padding: 0;
    padding-left: 60px;
  }
  .wrap-banner .box-right {
    flex-shrink: unset;
  }
  .flat-sidebar .list-style-2 .images-style {
    width: 220px;
  }
  .flat-sidebar .list-style-2 .images-style img {
    -o-object-fit: cover;
       object-fit: cover;
  }
  .flat-sidebar .homeya-box .meta-list {
    gap: 15px;
  }
  .layout-wrap .flat-counter-v2 {
    grid-template-columns: 1fr 1fr;
  }
  .wrap-dashboard-content .box-fieldset {
    margin-bottom: 30px;
  }
}
@media only screen and (max-width: 1099px) {
  .wrap-service-v4 {
    flex-direction: column;
  }
  .wrap-service-v4 .inner-service-left {
    padding: 0px 200px;
  }
  .wrap-service-v4 .inner-service-left img {
    width: 100%;
  }
  .header-search .nav-outer .outer-search {
    width: auto;
    border: 0;
  }
  .header-search .nav-outer .outer-search .form-box {
    display: none;
  }
  .header-search .header-account {
    flex-grow: 1;
    justify-content: flex-end;
  }
  .wrapper-layout-3 .wrap-sidebar,
  .wrapper-layout-3 .wrap-inner {
    width: 100%;
    height: auto;
  }
  .wrapper-layout-3 .wrap-map {
    margin-top: 0;
  }
  .wrap-dashboard-content-2 .grid-4 {
    grid-template-columns: 1fr 1fr;
  }
}
@media only screen and (max-width: 991px) {
  .header-account {
    display: none;
  }
  .header-search .nav-outer .outer-search {
    display: none;
  }
  .header-search .btn-search-mb {
    display: flex;
  }
  .main-header .main-menu {
    display: none;
  }
  .main-header {
    padding: 15px;
  }
  .mobile-button {
    display: block !important;
  }
  .header-account ul {
    margin-right: 20px;
  }
  .header-account {
    margin-right: 50px;
  }
  .header-account .dropdown-menu {
    margin-top: 19px !important;
  }
  #navbarSupportedContent {
    display: block;
  }
  .header-style-3 .header-account {
    margin-right: 0;
  }
  .footer .wd-social span {
    display: none;
  }
  .footer-cl-1,
  .footer-cl-2 {
    margin-bottom: 50px;
    margin-left: 0;
    margin-right: 0;
  }
  .footer-cl-3,
  .footer-cl-4 {
    margin-left: 0px;
  }
  .flat-title-page h2 {
    font-size: 46px;
    line-height: 58px;
  }
  .flat-section {
    padding: 60px 0px 60px;
  }
  .flat-blog-list {
    padding-right: 0;
    margin-bottom: 40px;
  }
  .flat-latest-post .flat-blog-item {
    margin-bottom: 30px;
  }
  h1 {
    font-size: 60px !important;
    line-height: 78px;
  }
  h2 {
    font-size: 46px !important;
    line-height: 58px;
  }
  .flat-slider.home-1 .slider-content {
    padding: 100px 0px;
  }
  .flat-slider.home-1 .slider-content .subtitle {
    padding: 0px;
  }
  .flat-slider.home-2 .img-banner-right {
    display: none;
  }
  .flat-slider.home-2 .slider-content {
    padding: 80px 0px;
  }
  .flat-slider.home-2 .slider-content .heading .title,
  .flat-slider.home-2 .slider-content .heading .subtitle {
    padding-right: 0;
  }
  .flat-slider.home-3 .slider-content {
    padding: 80px 0px 0px;
  }
  .flat-slider.home-3 .slider-content .heading .title,
  .flat-slider.home-3 .slider-content .heading .subtitle {
    padding-right: 0;
  }
  .flat-slider.home-5 .thumbs-swiper-column1 {
    display: none;
  }
  .wd-find-select {
    flex-wrap: wrap;
    gap: 16px;
    padding: 20px;
  }
  .wd-find-select .inner-group {
    flex-wrap: wrap;
    padding: 0;
  }
  .wd-find-select .inner-group .form-style {
    border-right: 0;
  }
  .wd-find-select .inner-group .form-style label {
    margin-bottom: 8px;
  }
  .wd-find-select .inner-group .form-style .form-control,
  .wd-find-select .inner-group .form-style .nice-select {
    padding: 10px 16px;
    border: 1px solid #e4e4e4;
    border-radius: 8px;
    font-weight: 400;
  }
  .wd-find-select .inner-group .box-filter .filter-advanced {
    flex-direction: row;
    gap: 4px;
  }
  .wd-find-select .inner-group .box-filter .text-1 {
    font-size: 16px;
    line-height: 26px;
  }
  .wd-find-select .tf-btn {
    width: 100%;
    border-radius: 4px;
  }
  .wd-search-form .group-box {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  .wd-search-form .group-select {
    gap: 20px;
  }
  .wd-search-form .group-checkbox .group-amenities {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }
  .flat-location {
    padding-left: 15px;
    padding-right: 15px;
  }
  .flat-location .navigation {
    width: 44px;
    height: 44px;
  }
  .flat-location .navigation .icon {
    font-size: 18px;
  }
  .flat-location .navigation.swiper-nav-next {
    left: 15px;
  }
  .flat-location .navigation.swiper-nav-prev {
    right: 15px;
  }
  .wrap-service,
  .wrap-benefit {
    gap: 30px;
  }
  .tf-sw-testimonial {
    margin-top: 30px;
  }
  .flat-agents .box:nth-child(1), .flat-agents .box:nth-child(2) {
    margin-bottom: 30px;
  }
  .flat-latest-new .box:not(:last-child) .flat-blog-item {
    margin-bottom: 30px;
  }
  .wrap-service-v2 .box-left .list-view li {
    width: 50%;
  }
  .wrap-service-v2 .box-right {
    margin-top: 30px;
  }
  .flat-service-v3 .box:not(:last-child) {
    margin-bottom: 30px;
  }
  .flat-latest-new-v2 .box:nth-child(1), .flat-latest-new-v2 .box:nth-child(2) {
    margin-bottom: 30px;
  }
  .box-test-left {
    border-radius: 0;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
  }
  .box-test-right {
    border-radius: 0;
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px;
    margin-left: 0;
    padding-left: 60px;
  }
  .flat-service-v5 {
    padding-top: 460px;
    margin-top: -400px;
  }
  .flat-service-v5 .box:not(:last-child) {
    margin-bottom: 30px;
  }
  .flat-recommended-v2 {
    padding-top: 410px;
    margin-top: -400px;
  }
  .wrap-property-v2 {
    display: block;
  }
  .wrap-property-v2 .box-inner-left {
    width: 100%;
  }
  .wrap-property-v2 .box-inner-right {
    padding: 30px;
    width: 100%;
  }
  .wrap-property-v2 .box-inner-right .content-property .box-name .location {
    font-size: 16px;
    line-height: 26px;
  }
  .wrap-property-v2.style-1 .box-inner-right {
    padding: 20px;
  }
  .wrap-benefit-v2 .box-right {
    margin-top: 30px;
    padding-left: 0;
  }
  .wrap-service-v4 .inner-service-left {
    padding: 0px 100px;
  }
  .wrap-banner {
    display: block;
  }
  .wrap-banner .box-left {
    padding: 60px;
  }
  .wrap-banner .box-right img {
    width: 100%;
  }
  .nav-tab-privacy {
    margin-bottom: 40px;
  }
  .flat-pricing .box:nth-child(1), .flat-pricing .box:nth-child(2) {
    margin-bottom: 30px;
  }
  .offcanvas.canvas-menu {
    width: 300px;
  }
  .offcanvas.canvas-filter {
    display: none;
    height: 530px;
  }
  .offcanvas.canvas-filter .wd-find-select {
    padding: 0px;
    gap: 0;
  }
  .offcanvas.canvas-filter .wd-find-select .inner-group {
    padding: 20px;
    border-radius: 0px;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
  }
  .offcanvas.canvas-filter .wd-find-select .tf-btn {
    border-radius: 0px;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }
  .wrapper-layout .wrap-left {
    padding: 24px 15px !important;
  }
  .flat-filter-search-v2 .flat-tab-form {
    padding: 24px 15px;
  }
  .flat-filter-search-v2 .flat-tab-form .wd-find-select {
    padding: 0;
  }
  .flat-filter-search-v2 .flat-tab-form .wd-search-form {
    top: 100%;
  }
  .wrapper-layout-3 .wrap-sidebar .widget-filter-search {
    padding: 30px 15px;
  }
  .wrapper-layout-3 .wrap-inner {
    padding: 15px;
  }
  .flat-sidebar .list-style-2 .images-style {
    width: 330px;
  }
  .widget-sidebar {
    margin-bottom: 30px;
  }
  .flat-slider-detail-v1 .icon-box .item {
    width: 40px;
    height: 40px;
  }
  .flat-slider-detail-v1 .icon-box .item .icon {
    font-size: 24px;
  }
  .single-property-video {
    padding-right: 0;
  }
  .single-property-feature .wrap-feature {
    gap: 30px;
  }
  .wrapper-sidebar-right {
    margin-bottom: 0;
    margin-top: 30px;
    padding-left: 0;
  }
  .flat-property-detail .header-property-detail {
    padding: 30px 15px;
  }
  .widget-box-single {
    padding: 15px;
  }
  .widget-box-header-single .header-property-detail .content-top,
  .widget-box-header-single .header-property-detail .content-bottom {
    padding-left: 15px;
    padding-right: 15px;
  }
  .widget-box-header-single .single-property-overview,
  .widget-box-header-single .single-property-desc {
    padding-left: 15px;
    padding-right: 15px;
  }
  .single-property-nearby .grid-3 {
    grid-template-columns: 1fr 1fr;
    gap: 8px;
  }
  .single-property-gallery .box-navigation .navigation {
    width: 40px;
    height: 40px;
  }
  .single-property-gallery .box-navigation .navigation .icon {
    font-size: 18px;
  }
  .flat-property-detail-v4 .single-property-contact .grid-3 {
    grid-template-columns: 1fr;
  }
  .flat-slider-contact .content-wrap .content-left {
    padding-right: 0;
    margin-bottom: 30px;
  }
  .flat-contact .contact-content {
    padding-right: 0;
    margin-bottom: 30px;
  }
  .layout-wrap .main-content-inner .wrapper-content .wd-filter {
    grid-template-columns: 1fr 1fr;
  }
  .widget-box-3,
  .widget-box-2 {
    padding: 15px;
  }
  .widget-box-3.mess-box {
    margin-top: 30px;
  }
  .box-amenities-property {
    padding-right: 0;
  }
  .box-floor-property {
    padding: 15px;
  }
  .flat-account {
    padding: 20px;
  }
  .flat-banner-about .banner-video .btn-video .icon {
    font-size: 70px;
  }
}
@media only screen and (max-width: 767px) {
  .flat-tab-form .nav-tab-form .nav-link-item {
    padding: 15px;
  }
  .animationtext.slide .cd-words-wrapper {
    text-align: center;
  }
  h3 {
    font-size: 40px !important;
    line-height: 58px;
  }
  .header-account .flat-bt-top {
    display: none;
  }
  .header-account ul {
    margin-right: 0;
  }
  .wrap-form-comment .group-ip {
    grid-template-columns: 1fr;
  }
  .list-review-item {
    flex-wrap: wrap;
  }
  .flat-blog-detail .box-image {
    grid-template-columns: 1fr;
  }
  .flat-blog-detail .post-navigation {
    grid-template-columns: 1fr;
  }
  .flat-blog-detail .post-navigation .previous-post {
    padding-right: 0;
  }
  .flat-blog-detail .post-navigation .previous-post::after {
    content: none;
  }
  .flat-blog-detail .post-navigation .next-post {
    margin-top: 20px;
    text-align: left;
    padding-left: 0;
  }
  .comment-form .text-checkbox {
    font-size: 14px !important;
    line-height: 22px;
  }
  .flat-quote {
    padding: 24px 24px 24px 32px;
  }
  .flat-quote .quote {
    font-size: 22px;
    line-height: 28px;
  }
  .wd-search-form .group-checkbox .group-amenities {
    grid-template-columns: repeat(2, 1fr);
  }
  .wrap-service,
  .wrap-benefit {
    flex-wrap: wrap;
  }
  .grid-location {
    grid-template-areas: "item1 item2" "item3 item4" "item5 item5" "item6 item6";
    grid-gap: 20px;
  }
  .grid-location-v2 {
    grid-template-columns: 1fr;
  }
  .flat-pricing .box:not(:last-child) {
    margin-bottom: 30px;
  }
  .homeya-box.list-style-2 {
    display: block;
  }
  .homeya-box.list-style-2 .images-style {
    width: 100%;
  }
  .header-dashboard .header-account .box-avatar {
    margin-right: 0;
  }
  .widget-box-2 .grid-2,
  .widget-box-2 .grid-3 {
    grid-template-columns: 1fr;
  }
  .wrap-dashboard-content .col-md-3 .box-fieldset {
    margin-bottom: 30px;
  }
  .wrap-dashboard-content-2 .grid-4 {
    grid-template-columns: 1fr;
  }
  .header-account .dropdown-menu {
    margin-top: 21px !important;
  }
}
@media only screen and (max-width: 700px) {
  .single-property-info .inner-box .label {
    width: 40%;
  }
  .flat-gallery-single {
    grid-template-areas: "item1 item1" "item1 item1" "item2 item3" "item4 item5";
  }
}
@media only screen and (max-width: 655px) {
  .box-agent.style-2 {
    display: block;
  }
  .wrap-service-v4 .inner-service-left {
    padding: 0px 30px;
  }
  .wrap-service-v4 .inner-service-left .title {
    font-size: 14px;
    line-height: 24px;
  }
  .wrap-service-v4 .inner-service-left h6,
  .wrap-service-v4 .inner-service-left h4 {
    font-size: 20px !important;
    line-height: 28px;
  }
  .wrap-service-v4 .inner-service-left .box-avatar {
    left: -44px;
  }
  .wrap-service-v4 .inner-service-left .box-trader {
    right: -34px;
  }
  .wrap-service-v4 .inner-service-left .box-trader .content {
    padding: 5px 15px;
  }
  .layout-wrap .flat-counter-v2 {
    grid-template-columns: 1fr;
  }
}
@media only screen and (max-width: 575px) {
  .wd-search-form .group-select {
    grid-template-columns: 1fr;
  }
  .box-title-price {
    display: block !important;
  }
  .box-title-price .title-price {
    margin-bottom: 4px;
    display: inline-block;
  }
  .flat-slider.home-5 .info-box {
    right: 15px;
    left: 15px;
  }
  .homeya-box.list-style-1 {
    display: block;
  }
  .homeya-box.lg .archive-top h5 {
    font-size: 24px !important;
    line-height: 30px;
  }
  .homeya-box.lg .archive-top .content {
    padding-left: 20px;
    padding-right: 20px;
  }
  .homeya-box.lg .archive-top .content .note {
    font-size: 16px;
    line-height: 26px;
  }
  .homeya-box.lg .archive-top .content .desc p {
    font-size: 16px;
    line-height: 26px;
  }
  .homeya-box.lg .archive-top .content .meta-list {
    -moz-column-gap: 20px;
         column-gap: 20px;
  }
  .flat-agents .box:not(:last-child) {
    margin-bottom: 30px;
  }
  .wrap-service-v2 .box-left .list-view li {
    width: 100%;
  }
  .grid-location-v2 .box-location-v3 {
    gap: 15px;
  }
  .flat-latest-new-v2 .box:not(:last-child) {
    margin-bottom: 30px;
  }
  .box-test-right {
    padding: 20px !important;
  }
  .box-test-right .box-tes-item-v2 h5 {
    font-size: 20px !important;
    line-height: 32px;
  }
  .wrap-benefit-v2 .box-right {
    grid-template-columns: 1fr;
  }
  .wrap-service-v4 .inner-service-left .box-avatar {
    top: 20%;
    padding: 8px 16px 8px 8px;
    gap: 8px;
  }
  .wrap-service-v4 .inner-service-left .box-trader {
    bottom: 10%;
  }
  .wrap-service-v4 .box-service {
    gap: 15px;
  }
  .wrap-banner .box-left {
    padding: 30px;
  }
  .header-style-3 .register {
    display: none;
  }
  .canvas-filter .header-style-3 .logo-box {
    display: none;
  }
  .flat-categories-v3 {
    padding: 0px 35px;
  }
  .flat-categories-v3 .swiper-nav-prev {
    right: 15px;
  }
  .flat-categories-v3 .swiper-nav-next {
    left: 15px;
  }
  .box-title-listing .box-filter-tab .nav-tab-filter {
    width: 100%;
  }
  .widget-sidebar .widget-box {
    padding: 15px;
  }
  .widget-sidebar .widget-box .latest-property-item {
    gap: 10px;
  }
  .widget-sidebar .widget-box .latest-property-item .images-style {
    flex-shrink: 0;
  }
  .widget-sidebar .widget-box .latest-property-item .h7 {
    font-size: 18px;
  }
  .widget-sidebar .widget-box .latest-property-item .meta-list {
    gap: 8px;
  }
  .header-property-detail .content-bottom .info-box .meta {
    gap: 15px;
  }
  .single-property-nearby .box-nearby {
    gap: 8px;
    grid-template-columns: 1fr;
  }
  .single-property-nearby .box-nearby .label {
    width: auto;
    margin-right: 20px;
  }
  .single-property-loan .box-loan-calc .box-top {
    grid-template-columns: 1fr;
  }
  .single-property-loan .box-loan-calc .box-bottom {
    flex-wrap: wrap;
    gap: 15px;
  }
  .col-sm-4 .attachments-item:first-child,
  .col-sm-6 .attachments-item:first-child {
    margin-bottom: 15px;
  }
  .single-property-map .info-map {
    justify-content: space-between;
    gap: 20px;
  }
  .box-contact-v2 {
    padding: 20px;
  }
  .flat-contact .contact-content .grid-2 {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  .layout-wrap .main-content-inner .wrapper-content .wd-filter {
    grid-template-columns: 1fr;
  }
  h1,
  h2,
  h3 {
    font-size: 40px !important;
    line-height: 58px;
  }
  h4 {
    font-size: 32px !important;
    line-height: 40px;
  }
  .layout-wrap .box-agent-avt {
    flex-wrap: wrap;
  }
  .homeya-box.list-style-1 .images-style {
    height: 220px;
  }
}
@media only screen and (max-width: 500px) {
  .box-amenities-property,
  .single-property-feature .wrap-feature {
    display: grid;
  }
}
@media only screen and (max-width: 400px) {
  .header-style-3 .header-account {
    display: flex;
  }
}
svg {
  stroke-width: 1.5;
}

.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  line-height: 1.2;
  color: #161e2d;
}

.show-admin-bar .close-btn,
.show-admin-bar .mobile-menu {
  margin-top: 40px;
}
.show-admin-bar .fixed-header {
  top: 40px;
}
.show-admin-bar .fixed-sidebar {
  top: 140px;
}
.show-admin-bar .fixed-cate-single {
  top: 120px;
}
.show-admin-bar .fixed-sidebar-2 {
  top: 180px;
}

.line-clamp-1 {
  -webkit-line-clamp: 1;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  -webkit-line-clamp: 2;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  -webkit-line-clamp: 3;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.btn-loading {
  position: relative;
  color: transparent !important;
  text-shadow: none !important;
  pointer-events: none;
}
.btn-loading:after {
  content: "";
  display: inline-block;
  vertical-align: text-bottom;
  border: 2px var(--bs-border-style) currentColor;
  border-right-color: transparent;
  border-radius: 100rem;
  color: #fff;
  position: absolute;
  width: 1.25rem;
  height: 1.25rem;
  left: calc(50% - 0.625rem);
  top: calc(50% - 0.625rem);
  animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}
.loading-spinner {
  align-items: center;
  background: hsla(0, 0%, 100%, 0.5);
  display: flex;
  height: 100%;
  inset-inline-start: 0;
  justify-content: center;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 1;
}
.loading-spinner:after {
  animation: loading-spinner-rotation 0.5s linear infinite;
  border-color: var(--primary-color) transparent var(--primary-color) transparent;
  border-radius: 50%;
  border-style: solid;
  border-width: 1px;
  content: " ";
  display: block;
  height: 40px;
  position: absolute;
  top: calc(50% - 20px);
  width: 40px;
  z-index: 1;
}

@keyframes loading-spinner-rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.rating-star {
  --bb-rating-size: 80px;
  height: calc(var(--bb-rating-size) / 5);
  position: relative;
  width: var(--bb-rating-size);
}
.rating-star:before {
  background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%20stroke-width%3D%222%22%20stroke%3D%22currentColor%22%20fill%3D%22none%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%3E%3Cpath%20stroke%3D%22none%22%20d%3D%22M0%200h24v24H0z%22%20fill%3D%22none%22%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M8.243%207.34l-6.38%20.925l-.113%20.023a1%201%200%200%200%20-.44%201.684l4.622%204.499l-1.09%206.355l-.013%20.11a1%201%200%200%200%201.464%20.944l5.706%20-3l5.693%203l.1%20.046a1%201%200%200%200%201.352%20-1.1l-1.091%20-6.355l4.624%20-4.5l.078%20-.085a1%201%200%200%200%20-.633%20-1.62l-6.38%20-.926l-2.852%20-5.78a1%201%200%200%200%20-1.794%200l-2.853%205.78z%22%20stroke-width%3D%220%22%20fill%3D%22%23ced4da%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E");
  background-repeat: repeat-x;
  background-size: calc(var(--bb-rating-size) / 5);
  bottom: 0;
  content: "";
  display: block;
  height: calc(var(--bb-rating-size) / 5);
  position: absolute;
  inset-inline-start: 0;
  inset-inline-end: 0;
  top: 0;
  width: var(--bb-rating-size);
}
.rating-star > span {
  display: block;
  width: var(--bb-rating-size);
  height: calc(var(--bb-rating-size) / 5);
  position: absolute;
  overflow: hidden;
}
.rating-star > span:before {
  background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%20stroke-width%3D%222%22%20stroke%3D%22currentColor%22%20fill%3D%22none%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%3E%3Cpath%20stroke%3D%22none%22%20d%3D%22M0%200h24v24H0z%22%20fill%3D%22none%22%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M8.243%207.34l-6.38%20.925l-.113%20.023a1%201%200%200%200%20-.44%201.684l4.622%204.499l-1.09%206.355l-.013%20.11a1%201%200%200%200%201.464%20.944l5.706%20-3l5.693%203l.1%20.046a1%201%200%200%200%201.352%20-1.1l-1.091%20-6.355l4.624%20-4.5l.078%20-.085a1%201%200%200%200%20-.633%20-1.62l-6.38%20-.926l-2.852%20-5.78a1%201%200%200%200%20-1.794%200l-2.853%205.78z%22%20stroke-width%3D%220%22%20fill%3D%22%23FFB342%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E");
  background-repeat: repeat-x;
  background-size: calc(var(--bb-rating-size) / 5);
  bottom: 0;
  content: "";
  display: block;
  height: calc(var(--bb-rating-size) / 5);
  width: var(--bb-rating-size);
  position: absolute;
  inset-inline-start: 0;
  inset-inline-end: 0;
  top: 0;
}

.error-page {
  padding: 10rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
@media (min-width: 768px) {
  .error-page {
    padding: 12rem 0;
  }
}
@media (min-width: 992px) {
  .error-page {
    padding: 18rem 0;
  }
}
.error-page .error-header {
  font-size: 5rem;
  margin-bottom: 0.5rem;
}
.error-page .error-title {
  font-size: 1.1rem;
  margin-bottom: 2rem;
}
@media (min-width: 768px) {
  .error-page .error-title {
    font-size: 1.25rem;
  }
}

.auth-card {
  position: relative;
  padding: 60px;
  border-radius: 24px;
  background-color: #f7f7f7;
}
.auth-card .card-header {
  margin-bottom: 24px;
}
.auth-card .card-header h3 {
  line-height: 1;
}
.auth-card .card-body,
.auth-card .card-header {
  padding: 0 !important;
}
.auth-card form label {
  color: #5c6368;
}
.auth-card form .auth-input-icon {
  background: transparent;
  border: 0;
  left: 1px;
  position: absolute;
  top: 8px;
  z-index: 10;
}
.auth-card form .btn-auth-submit {
  font-family: var(--heading-font);
  font-size: 16px;
  line-height: 26px;
  font-weight: 700;
  text-align: center;
  padding: 10px 20px;
  border-radius: 4px;
  border: 1px solid var(--primary-color);
  transition: all 0.3s ease;
  text-decoration: none;
  background-color: var(--primary-color);
  color: #ffffff;
}
.auth-card form .btn-auth-submit:hover {
  color: #ffffff;
  border-color: var(--primary-color);
  background-color: var(--hover-color);
}

.main-header {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.main-header .main-menu .navigation > li > a {
  color: var(--main-header-text-color);
}

.mobile-menu .mobi-icon-box .icon {
  color: #161e2d;
}

.cd-words-wrapper {
  display: inline-block;
  position: relative;
  text-align: left;
}

.cd-words-wrapper .item-text {
  display: inline-block;
  position: absolute;
  white-space: nowrap;
  left: 0;
  top: 0;
  font-weight: inherit;
}

.cd-words-wrapper .item-text.is-visible {
  position: relative;
}

.no-js .cd-words-wrapper .item-text {
  opacity: 0;
}

.no-js .cd-words-wrapper .item-text.is-visible {
  opacity: 1;
}

.animationtext.clip span {
  display: inline-block;
  padding: 0;
}

.animationtext.clip .cd-words-wrapper {
  overflow: hidden;
  vertical-align: top;
}

.animationtext.clip .cd-words-wrapper::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 6px;
  height: 100%;
  background-color: var(--primary-color);
}

.animationtext.clip .item-text {
  opacity: 0;
}

.animationtext.clip .item-text.is-visible {
  opacity: 1;
}

.animationtext.slide span {
  display: inline-block;
  /* padding: .2em 0; */
}

.animationtext.slide .cd-words-wrapper {
  overflow: hidden;
  vertical-align: top;
}

.animationtext.slide .item-text {
  opacity: 0;
  top: 0.2em;
}

.animationtext.slide .item-text.is-visible {
  top: 0;
  opacity: 1;
  animation: slide-in 0.6s;
}

.animationtext.slide .item-text.is-hidden {
  animation: slide-out 0.6s;
}
@keyframes slide-in {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  60% {
    opacity: 1;
    transform: translateY(20%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes slide-out {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  60% {
    opacity: 0;
    transform: translateY(120%);
  }
  100% {
    opacity: 0;
    transform: translateY(100%);
  }
}
.flat-blog-item .img-style img {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.flat-banner-blog img {
  height: 100%;
  max-height: 60rem;
  -o-object-fit: cover;
     object-fit: cover;
}

.ck-content blockquote {
  all: unset;
}

.flat-quote .quote {
  display: block;
}

.box-icon.social svg {
  width: 1.25rem;
  height: 1.25rem;
  stroke-width: 1.5;
}

.box-service.style-1 .icon-box {
  min-width: 80px;
}

.leaflet-pane .map-marker-home {
  background-color: unset;
  border: unset;
  background-image: url(https://themesflat.co/html/homzen/images/location/map-icon.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

.leaflet-container a {
  font-size: 1rem;
  line-height: 1.5;
}
.leaflet-container .leaflet-popup-content-wrapper {
  overflow: hidden;
  padding: 0 !important;
  border-radius: 1rem !important;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}
.leaflet-container .leaflet-popup-content-wrapper .leaflet-popup-content {
  margin: 0 !important;
  min-width: 320px;
  width: 320px;
}
.leaflet-container .leaflet-popup-content-wrapper .leaflet-popup-content .map-listing-item .bg-white {
  border-radius: 1rem;
  box-shadow: none;
}
.leaflet-container .leaflet-popup-content-wrapper .leaflet-popup-content .map-popup-content {
  display: flex;
  align-items: start;
  gap: 8px;
}
.leaflet-container .leaflet-popup-content-wrapper .leaflet-popup-content .map-popup-content-thumb {
  display: block;
  position: relative;
  width: 100px;
}
.leaflet-container .leaflet-popup-content-wrapper .leaflet-popup-content .map-popup-content-thumb img {
  width: 100%;
  height: 100%;
  border-radius: var(--bs-border-radius);
}
.leaflet-container .leaflet-popup-content-wrapper .leaflet-popup-content .map-popup-content-thumb span {
  position: absolute;
  top: 0.25rem;
  inset-inline-end: 0.25rem;
  font-size: 0.5rem;
}
.leaflet-container .leaflet-popup-content-wrapper .leaflet-popup-content .map-popup-content__details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}
.leaflet-container .leaflet-popup-content-wrapper .leaflet-popup-content .map-popup-content__title {
  font-size: 1rem;
  line-height: 1;
}
.leaflet-container .leaflet-popup-content-wrapper .leaflet-popup-content .map-popup-content__features {
  display: flex;
  align-items: center;
  gap: 1rem;
}
.leaflet-container .leaflet-popup-content-wrapper .leaflet-popup-content .map-popup-content__feature {
  display: flex;
  align-items: center;
  gap: 4px;
}
.leaflet-container .leaflet-popup-content-wrapper .leaflet-popup-content .map-popup-content svg {
  width: 1.25rem;
  height: 1.25rem;
}
.leaflet-container .leaflet-popup-content-wrapper .leaflet-popup-content .map-popup-content__price {
  font-weight: 500;
  color: var(--primary-color);
}
.leaflet-container .leaflet-popup-content-wrapper .leaflet-popup-content p {
  margin: unset;
}
.leaflet-container .custom-popup .leaflet-popup-content-wrapper {
  border-radius: 1rem !important;
}
.leaflet-container .custom-popup .leaflet-popup-tip {
  display: none !important;
}

.gl-star-rating .invalid-feedback {
  display: none !important;
}

.single-property-project .box-project {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}
@media (min-width: 767px) {
  .single-property-project .box-project {
    flex-wrap: nowrap;
  }
}
.single-property-project .project-thumb {
  min-width: 300px;
}
.single-property-project .project-thumb img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 16px;
}
.single-property-project .project-info .title a {
  font-size: 1.5rem;
  font-weight: 700;
}
.single-property-project .project-info .title a:hover {
  color: var(--primary-color);
}
.single-property-project .project-info ul.meta {
  margin-top: 0.5rem;
  display: grid;
  row-gap: 0.25rem;
}
.single-property-project .project-info ul.meta li {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--bs-gray-600);
}
.single-property-project .project-info ul.meta li svg {
  width: 1.25rem;
  height: 1.25rem;
}

@media (min-width: 768px) {
  .list-style-1 .images-style {
    max-width: 220px;
  }
}
@keyframes ripple {
  70% {
    box-shadow: 0 0 0 8px rgba(255, 255, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}
@keyframes rotated {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
.search-suggestion {
  overflow: auto;
  max-height: 20rem;
  padding: 0.5rem 0;
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  border: 1px solid #e4e4e4;
  position: absolute;
  top: 44px;
  z-index: 9999;
  width: 100%;
}
.search-suggestion .search-suggestion-item {
  padding: 0.5rem 1rem;
  cursor: pointer;
}
.search-suggestion .search-suggestion-item .search-suggestion-content h6 {
  font-size: 1rem;
  font-weight: 700;
  color: var(--bs-gray-800);
  line-height: 1.5;
}
.search-suggestion .search-suggestion-item .search-suggestion-content p {
  font-size: 0.875rem;
  color: var(--bs-gray-700);
}
.search-suggestion .search-suggestion-item:hover {
  background-color: var(--primary-color);
  color: #fff;
}
.search-suggestion .search-suggestion-item:hover .search-suggestion-content h6 {
  color: #fff;
}
.search-suggestion .search-suggestion-item:hover .search-suggestion-content p {
  color: #fff;
}
.search-suggestion .search-suggestion-item .search-suggestion-image {
  display: block;
  width: 60px;
  height: 60px;
  flex-shrink: 0;
}
.search-suggestion .search-suggestion-item .search-suggestion-image img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: var(--bs-border-radius);
}

@media (min-width: 768px) {
  .box-agent.style-2 .box-img {
    max-width: 20rem;
  }
}

.ck-content .box-location .image {
  all: unset;
}

h2.section-title {
  font-size: var(--h4-size);
}

.property-share-social {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: flex-end;
  margin-top: 2rem;
}
.property-share-social .list-social {
  gap: 0.5rem;
}
.property-share-social .list-social .social {
  background-color: var(--primary-color);
}

.flat-filter-search-v2 .form-sl {
  width: 100%;
}
.flat-filter-search-v2 .flat-tab-form .wd-search-form {
  top: 100%;
  width: 93%;
}
@media (min-width: 768px) {
  .flat-filter-search-v2 .flat-tab-form .wd-search-form {
    width: 97%;
  }
}

.leaflet-pane {
  z-index: 0 !important;
}

.leaflet-top, .leaflet-bottom {
  z-index: 1 !important;
}

@media (max-width: 767px) {
  .cd-words-wrapper {
    line-height: 1.5;
    width: 100% !important;
  }
}

.footer-cl-1 p a {
  color: white;
}

.boxmarker {
  background-color: var(--primary-color);
  border-radius: var(--bs-border-radius);
  color: #fff;
  display: inline-block;
  font-weight: 700;
  padding: 2px 5px;
  text-align: center;
  white-space: nowrap;
  width: auto !important;
}

.hero-banner-4 .wrap-filter-search {
  margin-top: -6.25rem;
}

.top-header {
  display: none;
  justify-content: space-between;
  padding: 8px 30px;
  font-size: 14px;
}
.top-header a {
  color: inherit;
}
@media (min-width: 992px) {
  .top-header {
    display: flex;
  }
}
.top-header .top-header-left, .top-header .top-header-right {
  display: flex;
  gap: 1.5rem;
}
.top-header .top-header-left .ae-anno-announcement-wrapper, .top-header .top-header-right .ae-anno-announcement-wrapper {
  padding-top: 0;
  padding-bottom: 0;
}
.top-header .top-header-item {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.dropdown-item.active, .dropdown-item:active {
  background-color: var(--primary-color);
  color: #fff;
}

.agent-detail-section {
  margin: 2rem 0;
}
.agent-detail-section .agent-header {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
  background-color: #f7f7f7;
  padding: 1.5rem;
  border-radius: 12px;
}
.agent-detail-section .agent-header .agent-avatar {
  width: 200px;
  border-radius: 50%;
  overflow: hidden;
}
.agent-detail-section .agent-header .agent-avatar img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.agent-detail-section .agent-header .agent-name {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
}
.agent-detail-section .agent-header .agent-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}
.agent-detail-section .agent-header .agent-company {
  font-size: 1rem;
}
.agent-detail-section .agent-header .agent-company strong {
  font-weight: 700;
}
.agent-detail-section .agent-header .agent-contact-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}
.agent-detail-section .agent-header .agent-contact-info a:hover {
  color: var(--primary-color);
}
.agent-detail-section .agent-header .agent-info-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}
@media (min-width: 768px) {
  .agent-detail-section .agent-header .agent-info-item {
    width: auto;
  }
  .agent-detail-section .agent-header .agent-info-item:after {
    content: "";
    display: block;
    width: 1px;
    height: 1.25rem;
    background-color: var(--bs-gray-300);
    margin-inline-start: 0.25rem;
  }
  .agent-detail-section .agent-header .agent-info-item:last-child:after {
    display: none;
  }
}
.agent-detail-section .agent-header .agent-info-item svg {
  width: 1.25rem;
  height: 1.25rem;
}
.agent-detail-section .agent-header .agent-social {
  display: flex;
  gap: 0.5rem;
}
.agent-detail-section .agent-header .agent-social a:hover {
  color: var(--primary-color);
}
.agent-detail-section .agent-header .agent-social a svg {
  width: 1.25rem;
  height: 1.25rem;
}
.agent-detail-section .agent-about-section, .agent-detail-section .agent-properties-section {
  margin: 2rem 0;
}
.agent-detail-section .agent-about-section h5, .agent-detail-section .agent-properties-section h5 {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1;
}
.agent-detail-section .agent-about-section {
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--bs-gray-300);
}

body[dir=rtl] .box-navigation {
  flex-direction: row-reverse;
  justify-content: flex-end;
}
body[dir=rtl] .nice-select:after {
  right: inherit !important;
  inset-inline-end: 24px !important;
}
body[dir=rtl] .flat-tab .wd-find-select .tf-btn {
  border-radius: 4px 0 0 4px;
}
body[dir=rtl] .main-header .main-menu .navigation > li > ul > li > a:before {
  inset-inline-start: 12px !important;
  top: 20px !important;
  transform: rotate(90deg);
}
body[dir=rtl] .box-title-listing .box-filter-tab .nice-select {
  padding: 10px 16px 10px 63px;
}
body[dir=rtl] .subscribe-form input {
  padding: 9px 28px 9px 70px;
}
body[dir=rtl] .wd-find-select.no-left-round {
  border-top-left-radius: 12px;
  border-top-right-radius: 0;
}
body[dir=rtl] .btn-view .icon {
  transform: rotate(180deg);
}
body[dir=rtl] .wd-filter-select {
  border-top-left-radius: 12px;
  border-top-right-radius: 0;
}
body[dir=rtl] .flat-pagination li:first-child .page-numbers svg {
  transform: rotate(180deg);
}
body[dir=rtl] .flat-pagination li:last-child .page-numbers svg {
  transform: rotate(180deg);
}
body[dir=rtl] .nav-tab-privacy .nav-link-item {
  padding: 10px 16px 8px 0;
}
body[dir=rtl] .footer-cl-1 {
  margin-right: unset;
  margin-left: 20.4%;
}

.career-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
}
.career-meta .career-meta-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  color: var(--bs-gray-600);
  margin-bottom: 0.5rem;
}
.career-meta .career-meta-item svg {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--primary-color);
}

.career-list .career-item {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid #e4e4e4;
  border-radius: 12px;
  padding: 16px;
  position: relative;
  overflow: hidden;
  height: 100%;
}
.career-list .career-item .career-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  line-height: 1;
}
.career-list .career-item .career-title a:hover {
  color: var(--primary-color);
}
.career-list .career-item .career-description {
  font-size: 0.875rem;
  color: var(--bs-gray-600);
}

.career-single .career-single-content {
  margin-bottom: 4rem;
}
.career-single .career-single-content .career-name {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1;
}
.career-single .career-single-content .career-meta {
  margin-bottom: 1rem;
}
.career-single .career-single-content .career-content p {
  font-size: 1rem;
  margin-bottom: 0.5rem;
}
.career-single .career-single-content .career-content h1, .career-single .career-single-content .career-content h2, .career-single .career-single-content .career-content h3, .career-single .career-single-content .career-content h4, .career-single .career-single-content .career-content h5, .career-single .career-single-content .career-content h6 {
  font-weight: 700;
  margin: 1rem 0 0.5rem;
  line-height: 1;
}
.career-single .career-single-content .career-content ul, .career-single .career-single-content .career-content ol {
  padding-inline-start: 1rem;
}
.career-single .career-single-content .career-content ul li, .career-single .career-single-content .career-content ol li {
  margin-bottom: 0.5rem;
}
.career-single .career-single-content .career-content ul, .career-single .career-single-content .career-content ol {
  list-style-type: disc;
}
.career-single .career-single-content .career-content ul li, .career-single .career-single-content .career-content ol li {
  list-style-type: disc;
}
.career-single .career-single-content .career-content ol {
  list-style-type: decimal;
}
.career-single .career-single-content .career-content ol li {
  list-style-type: decimal;
}
.career-single .career-related .career-related-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1;
}

@media (max-width: 768px) {
  .coming-soon-box {
    padding: 0 15px;
  }
}
.coming-soon-box .coming-soon-countdown-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}
@media (min-width: 768px) {
  .coming-soon-box .coming-soon-countdown-inner {
    justify-content: flex-start;
  }
}
.coming-soon-box .coming-soon-countdown-inner li {
  color: #fff;
  position: relative;
  width: 80px;
  height: 80px;
  font-size: 35px;
  font-weight: 700;
  padding: 10px;
  background-color: var(--primary-color);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
}
.coming-soon-box .coming-soon-countdown-inner li .label {
  display: block;
  text-transform: capitalize;
  margin-top: -15px;
  font-size: 16px;
  font-weight: normal;
  color: var(--titleColor);
}
.coming-soon-box .coming-soon-countdown-inner li:last-child {
  margin-right: 0;
}
.coming-soon-box .coming-soon-countdown-inner li:last-child::before {
  display: none;
}
.coming-soon-box .coming-soon-countdown-inner li:first-child {
  margin-left: 0;
}
.coming-soon-box .coming-soon-countdown-inner li::before {
  content: "";
  position: absolute;
  right: -50px;
  top: -10px;
  font-size: 70px;
  color: #ffffff;
}
.coming-soon-box .subscribe-form {
  margin-bottom: 1.5rem;
}
.coming-soon-box .subscribe-form .input-group {
  margin-bottom: 0 !important;
}
.coming-soon-box .subscribe-form .input-group .form-control {
  border-top-right-radius: 12px !important;
  border-bottom-right-radius: 12px !important;
}
.coming-soon-box .subscribe-form .input-group .btn {
  height: 100%;
  position: absolute;
  background: var(--primary-color);
  padding: 10px 20px;
  z-index: 9;
}
.coming-soon-box .subscribe-form .invalid-feedback {
  position: inherit;
}
.coming-soon-box .coming-soon-image {
  border-radius: 12px;
  overflow: hidden;
}
.coming-soon-box .coming-soon-image img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.auth-card form .form-control {
  line-height: 20px;
}
.auth-card form .auth-input-icon {
  top: 6px;
}

.listing-no-map .flat-title-page {
  padding: 50px 0 100px;
}

.flat-property-detail-v2 .content-bottom {
  flex-wrap: nowrap;
}

.image-sw-single img {
  width: 100%;
}

.bd-callout-info {
  --bd-callout-color: var(--bs-info-text-emphasis);
  --bd-callout-bg: var(--bs-info-bg-subtle);
  --bd-callout-border: var(--bs-info-border-subtle);
}

.bd-callout {
  --bs-link-color-rgb: var(--bd-callout-link);
  --bs-code-color: var(--bd-callout-code-color);
  padding: 1.25rem;
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
  color: var(--bd-callout-color, inherit);
  background-color: var(--bd-callout-bg, var(--bs-gray-100));
  border-left: 0.25rem solid var(--bd-callout-border, var(--bs-gray-300));
}

.single-detail.ck-content p {
  font-size: 16px;
  line-height: 32px;
  color: #161e2d;
  margin-bottom: 1rem;
}
.single-detail.ck-content ul:not([class]), .single-detail.ck-content ol:not([class]) {
  padding-left: 15px;
}
.single-detail.ck-content ul:not([class]) li, .single-detail.ck-content ol:not([class]) li {
  font-size: 16px;
}
.single-detail.ck-content ul:not([class]) li {
  list-style-type: disc;
}
.single-detail.ck-content ol:not([class]) li {
  list-style-type: decimal;
}
.single-detail.ck-content strong {
  font-weight: bolder;
}
.single-detail.ck-content a:hover {
  color: var(--primary-color);
}

.title a:hover, .post-author a:hover, .post-author span:not(:first-child) a:hover, .post-navigation a:hover, .single-property-contact .box-avatar a:hover {
  color: var(--primary-color);
}

.box-pricing h4 {
  margin-bottom: 0;
}

.newsletter-popup .modal-dialog .modal-content .modal-title {
  font-size: 1.5rem !important;
}
.newsletter-popup .modal-dialog .modal-content .modal-text {
  margin-bottom: 30px;
}
.newsletter-popup .modal-dialog .modal-content .btn-primary {
  background-color: var(--primary-color);
  color: #ffffff;
  border-color: var(--primary-color);
  outline: none !important;
}
.newsletter-popup .modal-dialog .modal-content .btn-primary:hover {
  background-color: var(--hover-color);
}

label.required:after {
  color: #fc655e;
  content: " *";
}

.wd-search-form input.form-control {
  padding: 9px 16px;
}

.ae-anno-announcement-wrapper {
  position: relative;
}

.mobi-icon-box a:hover {
  color: var(--primary-color);
}
.mobi-icon-box .dropdown-item.active, .mobi-icon-box .dropdown-item:active {
  color: #fff;
}

.top-header {
  background-color: var(--top-header-background-color);
  color: var(--top-header-text-color);
}

.main-header, .main-header .main-menu {
  background-color: var(--main-header-background-color);
  color: var(--main-header-text-color);
}

.main-header {
  border-bottom: 1px solid var(--main-header-border-color);
}

.header-lower .tf-btn.primary {
  color: #fff !important;
}

.flat-location .swiper img {
  max-height: 550px;
}

.btn-filter-mobile {
  all: unset;
}
@media (min-width: 768px) {
  .btn-filter-mobile {
    display: none;
  }
}
.btn-filter-mobile svg {
  width: 32px;
  height: 32px;
}

@media (min-width: 768px) {
  .search-box-offcanvas-button {
    display: none;
  }
}
@media (max-width: 767px) {
  .flat-filter-search-v2 .flat-tab-form .wd-search-form {
    position: initial;
  }
}

.search-box-offcanvas .search-box-offcanvas-header {
  display: none;
}
@media (max-width: 767px) {
  .search-box-offcanvas.active {
    visibility: visible;
  }
  .search-box-offcanvas.active .search-box-offcanvas-content {
    transform: translateX(0);
  }
  .search-box-offcanvas.active .search-box-offcanvas-backdrop {
    background-color: rgba(34, 34, 34, 0.4);
    height: 100%;
    position: absolute;
    transition: opacity 0.2s linear, visibility 0.2s, width 2s ease-in;
    width: 100%;
    z-index: -1;
  }
}
@media (max-width: 767px) {
  .search-box-offcanvas {
    background-color: transparent !important;
    border-radius: 0;
    height: 100%;
    left: 0;
    margin-bottom: 0;
    overflow-x: hidden;
    overflow-y: scroll;
    padding: 0 !important;
    position: fixed;
    top: 0;
    visibility: hidden;
    width: 100%;
    z-index: 1200;
  }
  .search-box-offcanvas .search-box-offcanvas-content {
    margin-top: 0;
    background-color: #f6f6f6;
    height: 100%;
    max-width: 85%;
    overflow-x: hidden;
    overflow-y: scroll;
    padding-bottom: 50px;
    width: 100%;
    transform: translateX(-100%);
    transition: visibility 0.3s ease-in-out, transform 0.3s ease-in-out;
  }
  .search-box-offcanvas .search-box-offcanvas-content .search-box-offcanvas-header {
    position: sticky;
    top: 0;
    z-index: 999;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e4e4e4;
  }
  .search-box-offcanvas .search-box-offcanvas-content .search-box-offcanvas-header h3 {
    font-size: 1.25rem !important;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0;
  }
  .search-box-offcanvas .search-box-offcanvas-content .search-box-offcanvas-header .btn-close {
    width: 0.5rem;
    height: 0.5rem;
  }
  .search-box-offcanvas .search-box-offcanvas-content .fixed-sidebar {
    top: 0;
  }
  .search-box-offcanvas .search-box-offcanvas-content .flat-tab-form {
    border: initial;
  }
  .search-box-offcanvas .search-box-offcanvas-content .box-filter {
    display: none !important;
  }
  .search-box-offcanvas .search-box-offcanvas-content .wrap-filter-search {
    margin-top: -0.25rem;
  }
  .search-box-offcanvas .search-box-offcanvas-content .wd-search-form {
    display: unset !important;
    opacity: 1;
    visibility: visible;
    margin-top: 5px;
    background: initial;
    border: initial;
    padding-top: 0;
  }
  .search-box-offcanvas .search-box-offcanvas-content .wd-search-form .search-box-offcanvas-button {
    margin-top: 2rem;
  }
  .search-box-offcanvas .search-box-offcanvas-content .wd-find-select {
    background-color: initial;
    box-shadow: initial;
  }
  .search-box-offcanvas .search-box-offcanvas-content .wd-find-select .tf-btn {
    display: none;
  }
}

.wrap-form-comment textarea:disabled {
  background: rgba(0, 0, 0, 0.1);
  opacity: 1;
}

@media (max-width: 768px) {
  .property-share-social {
    justify-content: start;
    margin-bottom: 30px;
  }
}
.wishlist-count-wrapper {
  position: relative;
  display: inline-block;
  margin-left: 10px;
  margin-right: 10px;
}
.wishlist-count-wrapper .wishlist-count {
  right: 5px;
  font-size: 12px;
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 13px;
}

.homeya-box .images-group .box-icon {
  border-radius: 50%;
}

.bottom-canvas .mobi-icon-box {
  display: none !important;
}

.wd-find-select .inner-group .form-style:last-child {
  border-inline-end: none;
}

.btn-float-filter-mobile {
  position: fixed;
  bottom: 20px;
  z-index: 1100;
  left: 15%;
  right: 15%;
  width: 70%;
  display: none;
}

@media (max-width: 768px) {
  .btn-float-filter-mobile {
    display: block;
  }
}
.btn-contact-whatsapp {
  background-color: #25D366;
  border-color: #25D366;
  color: #fff !important;
  border-radius: 10px;
}
.btn-contact-whatsapp:hover {
  background-color: #128C7E;
  border-color: #128C7E;
}

.btn-contact-telegram {
  background-color: #0088cc;
  border-color: #0088cc;
  color: #fff !important;
  border-radius: 10px;
}
.btn-contact-telegram:hover {
  background-color: #005580;
  border-color: #005580;
}

.btn-contact-phone {
  background-color: #333333;
  border-color: #333333;
  color: #fff !important;
  border-radius: 10px;
}
.btn-contact-phone:hover {
  background-color: #000000;
  border-color: #000000;
}

.list-img-slide, .listing-img-wrapper {
  max-height: 230px;
  min-height: 230px;
  overflow: hidden;
  position: relative;
}
.list-img-slide .slick-dots, .listing-img-wrapper .slick-dots {
  bottom: 10px;
}
.list-img-slide .slick-next, .list-img-slide .slick-prev, .listing-img-wrapper .slick-next, .listing-img-wrapper .slick-prev {
  display: none;
}
.list-img-slide .slick-dots li, .listing-img-wrapper .slick-dots li {
  margin: 0;
  width: 15px;
  height: 15px;
}
.list-img-slide .slick-dots li button:before, .listing-img-wrapper .slick-dots li button:before {
  font-size: 11px;
  color: #fff;
  opacity: 0.75;
}
.list-img-slide .slick-dots li.slick-active button:before, .listing-img-wrapper .slick-dots li.slick-active button:before {
  font-size: 15px;
  color: #fff;
  opacity: 1;
}

.homeya-box .images-group:after {
  display: none !important;
}

.homeya-box:hover .images-style img {
  transform: scale(1);
}

.header-account .dropdown-toggle::after {
  display: none;
}

[data-bb-toggle=wishlist-count] {
  color: #ffffff;
  position: absolute;
  top: 13px;
  right: 10px;
  font-size: 9px;
  line-height: 10px;
  background: #f00;
  height: 10px;
  width: 10px;
  border-radius: 8px;
  padding: 0px 2px;
}

.spoken-language-filter-item .text-cb-amenities .flag-container {
  display: inline-flex;
  align-items: center;
  min-width: 20px;
}
.spoken-language-filter-item .text-cb-amenities .flag-container .flag {
  border-radius: 2px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  max-height: 16px;
  width: auto;
}
.spoken-language-filter-item .text-cb-amenities .language-name {
  font-size: 14px;
  line-height: 1.4;
}
.spoken-language-filter-item:hover .text-cb-amenities .flag-container .flag {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

/*# sourceMappingURL=style.css.map*/